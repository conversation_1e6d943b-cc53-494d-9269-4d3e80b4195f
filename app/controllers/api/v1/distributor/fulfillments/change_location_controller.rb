class Api::V1::Distributor::Fulfillments::ChangeLocationController < Api::V1::Distributor::BaseController
  before_action :set_fulfillment

  def update
    unless @fulfillment.supported_actions.include?("change_location")
      render json: {
               error: "Change location is not supported for this fulfillment"
             },
             status: :unprocessable_entity
      return
    end

    new_location = @distributor.locations.find(change_location_params[:new_location_id])
    
    begin
      @fulfillment.change_location!(new_location)
      render json: @fulfillment
    rescue StandardError => e
      Rails.logger.error "Error changing fulfillment location: #{e.message}"
      render json: { error: e.message }, status: :unprocessable_entity
    end
  end

  private

  def set_fulfillment
    @fulfillment = @distributor.fulfillments.find(params[:fulfillment_id])
  end

  def change_location_params
    params.require(:change_location).permit(:new_location_id)
  end
end
