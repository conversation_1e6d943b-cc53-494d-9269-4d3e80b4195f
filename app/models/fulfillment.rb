# == Schema Information
#
# Table name: fulfillments
#
#  id                           :bigint           not null, primary key
#  default_shipping_coordinator :string           default("distributor")
#  distributor_data             :jsonb
#  distributor_order_charge     :decimal(10, 2)   default(0.0)
#  distributor_order_discount   :decimal(10, 2)   default(0.0)
#  distributor_order_subtotal   :decimal(10, 2)   default(0.0)
#  distributor_order_tax        :decimal(10, 2)   default(0.0)
#  distributor_status           :string
#  financial_status             :string
#  first_party                  :boolean          default(FALSE)
#  quoted_service_charge        :decimal(10, 2)   default(0.0)
#  quoted_shipping_charge       :decimal(10, 2)   default(0.0)
#  quoted_tax                   :decimal(10, 2)   default(0.0)
#  request_status               :string           default("pending")
#  shipping_charge              :decimal(10, 2)   default(0.0)
#  status                       :string
#  store_data                   :jsonb
#  stripe_payment_intent        :string
#  stripe_payment_status        :string
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  distributor_location_id      :bigint           not null
#  distributor_order_id         :string
#  merchant_order_id            :bigint           not null
#  store_fulfillment_id         :string
#
# Indexes
#
#  index_fulfillments_on_distributor_location_id  (distributor_location_id)
#  index_fulfillments_on_merchant_order_id        (merchant_order_id)
#  index_fulfillments_on_mo_id_and_dl_id          (merchant_order_id,distributor_location_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (distributor_location_id => distributor_locations.id)
#  fk_rails_...  (merchant_order_id => merchant_orders.id)
#
class Fulfillment < ApplicationRecord
  include PgSearch::Model

  belongs_to :merchant_order, inverse_of: :fulfillments
  belongs_to :distributor_location

  has_many :line_items,
           dependent: :destroy,
           class_name: "FulfillmentLineItem",
           inverse_of: :fulfillment do
    def total_quantity
      sum(:quantity)
    end
  end
  has_many :units, through: :line_items, class_name: "FulfillmentUnit", inverse_of: :fulfillment
  has_many :deliveries,
           -> { order("created_at ASC") },
           dependent: :destroy,
           class_name: "FulfillmentDelivery",
           inverse_of: :fulfillment do
    def with_tracking
      where.not(tracking_number: [nil, "", "pending"])
    end
  end
  has_many :parcels,
           through: :deliveries,
           class_name: "FulfillmentParcel",
           inverse_of: :fulfillment do
    def items
      FulfillmentParcelItem.where(fulfillment_parcel_id: ids)
    end
  end
  %w[pending in_progress cancelled complete].each do |status|
    scope "#{status}", -> { where(status: status) }
  end

  has_many :shipment_line_items, class_name: "Shipping::ShipmentLineItem", dependent: :destroy
  has_many :shipments,
           -> { distinct },
           through: :shipment_line_items,
           class_name: "Shipping::Shipment"

  pg_search_scope :search_by_identifiers,
                  against: [:distributor_order_id],
                  associated_against: {
                    merchant_order: [:store_order_reference]
                  },
                  using: {
                    tsearch: {
                      prefix: true
                    }
                  }

  # distributor_status
  validates :distributor_status,
            presence: true,
            inclusion: {
              in: %w[pending submitting submitted submission_error in_progress cancelled complete]
            }
  %w[submitted in_progress cancelled complete].each do |status|
    scope "distributor_#{status}", -> { where(distributor_status: status) }
  end

  validates :request_status,
            inclusion: {
              in: %w[
                pending
                fulfillment_requested
                fulfillment_rejected
                fulfillment_accepted
                cancellation_requested
                cancellation_rejected
                cancellation_accepted
              ]
            }

  scope :with_transaction_id,
        ->(transaction_id) { where("distributor_data->>'transaction_id' = ?", transaction_id) }
  scope :order_by_distributor_location_code, -> { order("distributor_locations.code ASC") }

  # @liam-wallace is this used anywhere as it appears we don't set 'status' at all, only distributor_status
  scope :active, -> { where(status: ["in_progress"]) }

  has_many :events, dependent: :destroy, class_name: "FulfillmentEvent"

  delegate :distributor, to: :distributor_location
  delegate :store, to: :merchant_order
  delegate :recipient, to: :merchant_order
  delegate :sender, to: :distributor_location

  before_create :set_distributor_store_relationship, if: -> { distributor_location.present? }

  # call update_stripe_transfer_metadata after update if stripe_payment_intent is present
  after_update :update_stripe_transfer_metadata, if: -> { stripe_payment_intent.present? }

  after_commit :update_shipments, on: %i[update]
  after_commit :update_request_status, on: %i[update]

  has_one :revenue_share_fulfillment_unit
  after_update :create_revenue_share_unit, if: -> { distributor_status == "complete" }


  def self.search(query)
    query.present? ? search_by_identifiers(query) : order(created_at: :desc)
  end

  def set_distributor_store_relationship
    return unless distributor_account.present?

    self.first_party = distributor_account.first_party
  end

  def update_shipments
    shipments.each { |shipment| shipment.save }
  end

  def create_revenue_share_unit
    revenue_share_cents = Stock::Calculators::PartbotFeeCalculator.calculate(self)

    Rails.logger.info "Creating revenue_share_fulfillment_unit for fulfillment #{id}: #{revenue_share_cents}"

    # get or create the revenue_share_fulfillment_unit and update the price_cents
    revenue_share_fulfillment_unit =
      RevenueShareFulfillmentUnit.find_or_create_by(fulfillment: self)
    revenue_share_fulfillment_unit.update(price_cents: revenue_share_cents)
  end

  def tags
    [
      "distributor/#{distributor.id}/".downcase,
      "distributor_location/#{distributor_location.id}/".downcase
    ]
  end

  def distributor_account_code
    store.distributor_account(distributor)&.account_code ||
      store.team.default_distributor_account(distributor)&.account_code || "PARTBOT"
  end

  def default_shipping_coordinator
    if store.partbot_coordinating_shipping &&
         distributor_location.default_shipping_coordinator == "partbot"
      "partbot"
    else
      "distributor"
    end
  end

  def distributor_account
    store.distributor_account(distributor) || store.team.default_distributor_account(distributor)
  end

  def distribution_pending?
    distributor_status == "submitting" || distributor_status == "submitted" ||
      distributor_status == "pending" || distributor_status == "submission_error"
  end

  def distribution_open?
    distributor_status == "in_progress"
  end

  def distribution_closed?
    distributor_status == "complete" || distributor_status == "cancelled"
  end

  def distribution_complete?
    distributor_status == "complete"
  end

  def distribution_cancelled?
    distributor_status == "cancelled"
  end

  def mark_submitting!
    update!(distributor_status: "submitting")
  end

  def mark_submitted!(new_distributor_data)
    assign_attributes(
      distributor_data: distributor_data.merge(new_distributor_data),
      distributor_status: "submitted"
    )
    save!
    notify_distributor_on_fulfillment_request
  end

  def mark_submission_error!
    update!(distributor_status: "submission_error")
  end

  def request_cancellation
    if request_status == "pending"
      update(request_status: "cancellation_requested")
      notify_distributor_on_cancellation_request
    end
  end

  def cancellation_request_open?
    request_status == "cancellation_requested"
  end

  def cancellation_request_accepted?
    request_status == "cancellation_accepted"
  end

  def cancellation_request_rejected?
    request_status == "cancellation_rejected"
  end

  def update_request_status
    return unless distribution_closed? && cancellation_request_open?

    if distribution_cancelled?
      update(request_status: "cancellation_accepted")
      return
    end

    if distribution_complete?
      update(request_status: "cancellation_rejected")
      return
    end
  end

  def change_location!(new_location)
    ActiveRecord::Base.transaction do
      # check all shipments are in quoted, booked, or cancelled
      if shipments.any? { |shipment| !shipment.status.in?(%w[pending quoted booked cancelled]) }
        raise "Cannot change location if there are non-editable shipments"
      end

      # cancel all shipments
      shipments.each do |shipment|
        shipment.cancel_booking
        shipment.destroy
      end


      self.distributor_location = new_location
      self.save!
    end

    self.package!
  end

  def supported_actions
    actions = []
    # actions << "ready_to_ship" if ready_to_ship?
    actions << "respond_fulfillment_request" if distributor_status == "submitted"
    actions << "respond_cancellation_request" if cancellation_request_open? && distribution_open?
    actions << "change_location"

    actions
  end

  def total_tax
    # distributor_order_tax
    # sum the tax from the fulfillment line items + quoted_tax
    line_items.sum(&:line_tax) + quoted_tax
  end

  def total_subtotal
    # distributor_order_subtotal
    # sum the line_subtotal from the fulfillment line items
    line_items.sum(&:line_subtotal)
  end

  def total_charges
    # distributor_order_charge
    quoted_shipping_charge + quoted_service_charge
  end

  def total_discounts
    distributor_order_discount
  end

  def total_exc_tax
    total_subtotal + total_charges - total_discounts
  end

  def total_inc_tax
    total_exc_tax + total_tax
  end

  def payment_status
    return "order_cancelled" if distributor_status == "cancelled"
    return "direct_account" if distributor_account.payment_method == "direct_account"
    if distributor_account.payment_method == "credit_card" && stripe_payment_status.blank?
      return "unpaid"
    end
    if distributor_account.payment_method == "credit_card" && stripe_payment_status == "succeeded"
      return "paid"
    end
    return stripe_payment_status
  end

  def error_messages
    errors = []

    units
      .select { |u| u.error_codes.any? }
      .each do |unit|
        errors << {
          error_source: "unit",
          source_id: unit.id,
          label: unit.identifier,
          codes: unit.error_codes
        }
      end

    deliveries
      .select { |d| d.error_codes.any? }
      .each do |delivery|
        errors << {
          error_source: "delivery",
          source_id: delivery.id,
          label: "Shipment #{delivery.position}",
          codes: delivery.error_codes
        }
      end

    parcels
      .select { |p| p.error_codes.any? }
      .each do |parcel|
        errors << {
          error_source: "parcel",
          source_id: parcel.id,
          label: "Box #{parcel.position} of Shipment #{parcel.delivery.position}",
          codes: parcel.error_codes
        }
      end

    errors
  end

  def rejection_reason
    return distributor_data["rejection_reason"] if distributor_data.present?
    return nil
  end

  def rejection_message
    return distributor_data["rejection_message"] if distributor_data.present?
    return nil
  end

  def acceptance_message
    return distributor_data["acceptance_message"] if distributor_data.present?
    return nil
  end

  def tracking_company
    # default to shipments models if booked
    if shipments.where(status: "booked").any?
      return shipments.first.carrier_name if shipments.first.carrier_name.present?
      return nil
    end

    companies = deliveries.with_tracking.pluck(:carrier).compact.uniq.join(" / ")
    companies.present? ? companies : "Other"
  end

  def tracking_numbers
    if shipments.where(status: "booked").any?
      if shipments.any? { |shipment| shipment.carrier_tracking_number.present? }
        return shipments.pluck(:carrier_tracking_number).compact.uniq
      end

      return []
    end

    deliveries.with_tracking.pluck(:tracking_number).compact.uniq
  end

  def tracking_urls
    if shipments.where(status: "booked").any?
      if shipments.any? { |shipment| shipment.carrier_tracking_number.present? }
        return shipments.map(&:tracking_url).compact.uniq
      end

      return []
    end

    deliveries.with_tracking.pluck(:tracking_url).compact.uniq
  end

  def update_tracking
    store.update_tracking(self)
  end

  def use_order_shipping?
    return false unless first_party_order?
    distributor_location.pass_through_first_party_order_shipping?
  end

  def use_order_discounts?
    return false unless first_party_order?
    distributor_location.pass_through_first_party_order_discounts?
  end

  def first_party_order?
    first_party
  end

  def ready_to_ship?
    error_messages.empty? && deliveries.any?(&:bookable?)
  end

  def has_deliveries_without_parcels?
    deliveries.any? { |delivery| delivery.parcels.empty? }
  end

  def has_parcels_without_items?
    parcels.any? { |parcel| parcel.items.empty? }
  end

  def package!
    shipment = nil
    if shipments.any?
      shipment = shipments.first
    else
      shipment = Shipping::ShippingPlan.new(self, nil).build_shipment
    end

    # Deprecate
    Stock::FulfillmentPackager.new(self).package if deliveries.none?

    shipment.create_snapshot!(metadata: { from: "fulfillment" })
    shipment
  end

  # percentaage of the merchant order's line item total that this fulfillment represents
  def fill_percentage
    line_items.total_quantity.to_f / merchant_order.line_items.total_quantity.to_f
  end

  def distance_to_destination
    @distance_to_destination ||=
      distributor_location.distance_to(merchant_order.shipping_address_lat_long)
  end

  def team
    store.team
  end

  def shipping_company
    "" #tracking_company&.split(" - ")&.first
  end

  def shipping_service
    "" #tracking_company&.split(" - ")&.last
  end

  def packaging_status
    if fulfillment_items.all? { |item| item.packaged? }
      return "packaged"
    elsif fulfillment_items.any? { |item| item.packaged? }
      return "partially packaged"
    else
      return "not packaged"
    end
  end

  def create_fulfillment_units
    line_items.each do |line_item|
      if line_item.product.distributor_packages.where(distributor: distributor).any?
        line_item
          .product
          .distributor_packages
          .where(distributor: distributor)
          .each do |package|
            line_item.units << FulfillmentUnit.new(
              quantity: line_item.quantity,
              length: package.length_cm,
              width: package.width_cm,
              height: package.height_cm,
              dimensions_unit: "cm",
              weight: package.calculated_weight_kg,
              weight_unit: "kg",
              identifier: package.identifier,
              ships_separate: package.ships_separate
            )
          end
      elsif line_item.product.packages.any?
        line_item.product.packages.each do |package|
          line_item.units << FulfillmentUnit.new(
            quantity: line_item.quantity,
            length: package.length_cm,
            width: package.width_cm,
            height: package.height_cm,
            dimensions_unit: "cm",
            weight: package.calculated_weight_kg,
            weight_unit: "kg",
            identifier: package.identifier,
            ships_separate: package.ships_separate
          )
        end
      else
        line_item.units << FulfillmentUnit.new(
          quantity: line_item.quantity,
          length: nil,
          width: nil,
          height: nil,
          weight: nil,
          identifier: line_item.product.sku,
          ships_separate: false
        )
      end
    end
  end

  def recalculate_fullfillment_units
    line_items.each do |line_item|
      line_item.units.destroy_all
      if line_item.product.distributor_packages.where(distributor: distributor).any?
        line_item
          .product
          .distributor_packages
          .where(distributor: distributor)
          .each do |package|
            line_item.units << FulfillmentUnit.new(
              quantity: line_item.quantity,
              length: package.length_cm,
              width: package.width_cm,
              height: package.height_cm,
              dimensions_unit: "cm",
              weight: package.calculated_weight_kg,
              weight_unit: "kg",
              identifier: package.identifier,
              ships_separate: package.ships_separate
            )
          end
      elsif line_item.product.packages.any?
        line_item.product.packages.each do |package|
          line_item.units << FulfillmentUnit.new(
            quantity: line_item.quantity,
            length: package.length_cm,
            width: package.width_cm,
            height: package.height_cm,
            dimensions_unit: "cm",
            weight: package.calculated_weight_kg,
            weight_unit: "kg",
            identifier: package.identifier,
            ships_separate: package.ships_separate
          )
        end
      else
        line_item.units << FulfillmentUnit.new(
          quantity: line_item.quantity,
          length: nil,
          width: nil,
          height: nil,
          weight: nil,
          identifier: line_item.product.sku,
          ships_separate: false
        )
      end
    end
  end

  def split_into_another_fulfillment(fulfillment_parcel_items_to_move: [])
    ActiveRecord::Base.transaction do
      fulfillment = merchant_order.fulfillments.create(distributor_location: distributor_location)

      fulfillment_parcel_items_to_move.each do |fulfillment_parcel_item|
        fulfillment_item = fulfillment_parcel_item.fulfillment_item
        fulfillment_item.fulfillment = fulfillment
        fulfillment_item.save
      end

      return fulfillment
    end
  end

  def quote_line_items
    line_items.each do |line_item|
      line_item.unit_price = line_item.distributor_price
      line_item.calculate_totals
      line_item.save
    end
  end

  def line_items_charge
    line_items_charge = line_items.sum(&:line_total)

    return line_items_charge if line_items_charge > 0

    quote_line_items
    line_items.sum(&:line_total)
  end

  def sync_to_shipping_provider
    deliveries.each { |delivery| delivery.sync_to_shipping_provider }
  end

  def sync_from_shipping_provider
    deliveries.each { |delivery| delivery.sync_from_shipping_provider }
  end

  def submittable?
    return false if distributor.blank?
    return false unless distributor_status == "pending" || distributor_status == "submission_error"
    return false if distributor_data.present?
    return false if line_items.empty?

    true
  end

  def notify_store_on_deliveries_booked
    store.deliveries_booked(self)
  end

  def weight_kg
    deliveries.sum(&:weight_kg)
  end

  def json_response
    self.as_json(
      only: %i[id distributor_order_id distributor_status shipping_charge],
      methods: %i[shipping_company shipping_service supported_actions error_messages],
      include: [
        distributor_location: {
          only: %i[id name code distributor_id]
        },
        line_items: {
          only: %i[id quantity quantity_packed packaging],
          methods: %i[quantity_packed packaging],
          include: [
            merchant_order_line_item: {
              only: [:id],
              include: [product: { only: %i[id sku title] }]
            },
            units: {
              methods: %i[
                product
                formatted_dimensions
                formatted_weight
                packaging
                quantity_packed
                linked_product_package
              ]
            }
          ]
        },
        deliveries: {
          only: %i[
            id
            delivery_status
            delivery_method
            carrier
            carrier_service
            tracking_number
            tracking_url
            label_status
            label_url
            packing_slip_url
            multi_box_support_status
          ],
          methods: %i[
            identifier
            position
            supported_actions
            weight_kg
            formatted_weight_kg
            formatted_cubic_weight_kg
            total_quantity
            handover_status
          ],
          include: [
            parcels: {
              only: %i[id distributor_box_id weight weight_limit],
              include: [
                items: {
                  only: %i[id quantity fulfillment_unit_id],
                  include: [
                    fulfillment_unit: {
                      methods: %i[
                        product
                        formatted_dimensions
                        formatted_weight
                        packaging
                        quantity_packed
                      ]
                    }
                  ]
                }
              ],
              methods: %i[position dimensions packaging_method name units total_quantity]
            }
          ]
        }
      ]
    )
  end

  def create_payment_intent
    if distributor_account.payment_method == "credit_card"
      Stripe.api_key = ENV["STRIPE_API_KEY"]

      begin
        # if total_inc_tax is blank or 0, we need to get and set the distributor_* pricing values
        if total_inc_tax.blank? || total_inc_tax == 0
          # get distributor pricing
          Rails.logger.warn "total_inc_tax is blank or 0, getting distributor pricing..."
          return false
        end

        customer = Stripe::Customer.retrieve(merchant_order.store.team.stripe_customer_id)

        payment_intent =
          Stripe::PaymentIntent.create(
            customer: customer.id,
            confirm: true,
            amount: (total_inc_tax * 100).to_i,
            currency: "aud",
            automatic_payment_methods: {
              enabled: true,
              allow_redirects: "never"
            },
            payment_method: customer.invoice_settings&.default_payment_method,
            transfer_data: {
              destination:
                distributor_account.distributor.stripe_connect_account ||
                  ENV["STRIPE_DEFAULT_CONNECT_ACCOUNT"]
            },
            metadata: {
              partbot_fulfilment_id: self.id,
              distributor_order_id: self.distributor_order_id,
              distributor_account_code: self.distributor_account_code,
              store_order_reference: self.merchant_order&.store_order_reference
            }
          )

        # update the payment_intent id on the fulfillment
        self.update(stripe_payment_intent: payment_intent.id)
      rescue => exception
        Rails.logger.error "ERROR: #{exception}"
        PartbotMailer.send_email(
          email: ENV["ALERT_EMAIL"],
          message: "Error creating payment_intent for fulfillment #{self.id}: #{exception}",
          subject: "Stripe Payment Error"
        ).deliver_now
      end
    else
      Rails.logger.info "Distributor account #{distributor_account.id} payment method is not credit card"
      return false
    end
  end

  def capture_payment
    if merchant_order.store.auto_billing
      begin
        Stripe.api_key = ENV["STRIPE_API_KEY"]

        # create payment intent if stripe_payment_intent is blank
        create_payment_intent if stripe_payment_intent.blank?

        # retrieve the payment_intent
        payment_intent = Stripe::PaymentIntent.retrieve(stripe_payment_intent)

        Rails.logger.info "PAYMENT_INTENT: #{payment_intent}"

        # check if the payment_intent status is requires_capture
        if payment_intent.status == "requires_capture"
          # capture the payment
          Stripe::PaymentIntent.capture(stripe_payment_intent)
        end

        # update the payment_intent status on the fulfillment
        Rails.logger.info "Updating stripe_payment_status to #{payment_intent.status}"
        self.update(stripe_payment_status: payment_intent.status, financial_status: "paid")
      rescue => exception
        Rails.logger.error "ERROR: #{exception}"
        PartbotMailer.send_email(
          email: ENV["ALERT_EMAIL"],
          message: "Error capturing payment for fulfillment #{self.id}: #{exception}",
          subject: "Stripe Payment Error"
        ).deliver_now
      end
    else
      return false
    end
  end

  def update_stripe_transfer_metadata
    if stripe_payment_intent.present?
      Rails.logger.info "Updating Stripe transfer metadata for payment_intent #{stripe_payment_intent}"
      begin
        Stripe.api_key = ENV["STRIPE_API_KEY"]

        payment_intent = Stripe::PaymentIntent.retrieve(stripe_payment_intent)

        # find transfer id, check if id exists on payment_intent.charges.data.first.transfer
        transfer_id = payment_intent.charges&.data&.first&.transfer

        if transfer_id
          # update the metadata on the payment_intents transfer object
          Stripe::Transfer.update(
            transfer_id,
            metadata: {
              partbot_fulfilment_id: self.id,
              distributor_order_id: self.distributor_order_id,
              distributor_account_code: self.distributor_account_code,
              store_order_reference: self.merchant_order&.store_order_reference
            }
          )
        end
      rescue => exception
        Rails.logger.error "ERROR: #{exception}"
        PartbotMailer.send_email(
          email: ENV["ALERT_EMAIL"],
          message: "Error updating transfer metadata for payment_intent #{payment_intent.id}",
          subject: "Stripe Webhook Error"
        ).deliver_now
      end
    end
  end

  def get_rate_card_weight_bands
    Rails.logger.info "store.shipping_calculator: #{store.shipping_calculator}"
    if store.shipping_calculator == "flat_rate"
      Rails.logger.info "FULFILLMENT: #{self.id}"
      Rails.logger.info "DISTRIBUTOR_LOCATION: #{self.distributor_location.id}"
      Rails.logger.info "MERCHANT_ORDER: #{self.merchant_order.shipping_country_code}"

      # get distributor_location_carrier_rate from the store, if it exists
      distributor_location_carrier_rate =
        store.distributor_location_carrier_rates.find_by(
          distributor_location: self.distributor_location,
          country_code: self.merchant_order.shipping_country_code
        )

      if distributor_location_carrier_rate
        Rails.logger.info "Fulfillment: #{self.id} uses rate_card_weight_bands from: #{distributor_location_carrier_rate.id}"
        rate_card_weight_bands = distributor_location_carrier_rate.rate_card_weight_bands
      else
        Rails.logger.info "No distributor_location_carrier_rate found for fulfillment: #{self.id}"
      end
    else
      rate_card_weight_bands = nil
    end

    rate_card_weight_bands
  end

  private

  def notify_distributor_on_fulfillment_request
    message = <<~TEXT
      A new fulfillment request has been received for order #{merchant_order.store_order_reference}.
      
      Order Details:
      - Store Order Reference: #{merchant_order.store_order_reference}
      - Distributor Location: #{distributor_location.name}
      - Number of Items: #{line_items.sum(&:quantity)}
      
      Please review and process this request in Partbot as soon as possible.
      
      View in Partbot: #{ENV["PARTBOT_DISTRIBUTOR_URL"]}/locations/#{distributor_location.id}/fulfillments?page=1&page_size=25&query=#{merchant_order.store_order_reference}
    TEXT

    PartbotMailer.send_email(
      type: "text",
      email: distributor_location.contact_email,
      message: message,
      subject: "New Fulfillment Request - Order #{merchant_order.store_order_reference}"
    ).deliver_now
  end

  def notify_distributor_on_cancellation_request
    message = <<~HTML
      A cancellation request has been made for order #{merchant_order.store_order_reference} (Merchant ID) / #{distributor_order_id} (Your ID).
      <br /><br/>
      If able to cancel, you can do so directly in your ERP, and Partbot will automatically update the order status. 
      If fulfillment has progressed too far to cancel, please process the request in Partbot as soon as possible.
      <br /><br/>
      <a href="#{ENV["PARTBOT_DISTRIBUTOR_URL"]}/locations/#{distributor_location.id}/fulfillments?page=1&page_size=25&query=#{self.distributor_order_id}">View in Partbot</a>
    HTML

    PartbotMailer.send_email(
      type: "html",
      email: distributor_location.contact_email,
      subject: "Fulfillment Cancellation Request",
      message: message
    ).deliver_now
  end
end
