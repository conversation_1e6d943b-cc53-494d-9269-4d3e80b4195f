# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2025_06_01_140942) do

  # These are extensions that must be enabled in order to support this database
  enable_extension "citext"
  enable_extension "fuzzystrmatch"
  enable_extension "pg_trgm"
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "alternate_skus", force: :cascade do |t|
    t.string "productable_type"
    t.bigint "productable_id"
    t.string "sku"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["product_id"], name: "index_alternate_skus_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_alternate_skus_on_productable_type_and_productable_id"
  end

  create_table "amazon_listings", force: :cascade do |t|
    t.string "product_type"
    t.bigint "product_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "brand_id", null: false
    t.boolean "active", default: false
    t.index ["brand_id"], name: "index_amazon_listings_on_brand_id"
    t.index ["product_type", "product_id"], name: "index_amazon_listings_on_product_type_and_product_id"
  end

  create_table "api_keys", force: :cascade do |t|
    t.string "access_key", null: false
    t.string "secret_key", null: false
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["access_key"], name: "index_api_keys_on_access_key", unique: true
    t.index ["secret_key"], name: "index_api_keys_on_secret_key", unique: true
    t.index ["team_id"], name: "index_api_keys_on_team_id"
  end

  create_table "api_logs", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "endpoint"
    t.jsonb "body"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "status"
    t.string "remote_ip"
    t.index ["user_id"], name: "index_api_logs_on_user_id"
  end

  create_table "api_request_audits", force: :cascade do |t|
    t.string "request_type"
    t.string "request_body"
    t.string "response_code"
    t.decimal "cost", precision: 10, scale: 4
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "url"
    t.string "http_method"
    t.text "request_headers"
    t.text "response_body"
    t.text "response_headers"
    t.float "time_elapsed"
    t.jsonb "additional_attributes", default: {}
  end

  create_table "api_request_subscriptions", force: :cascade do |t|
    t.string "identifier", null: false
    t.datetime "start_date", null: false
    t.datetime "end_date", null: false
    t.boolean "active", default: true, null: false
    t.integer "total_requests", default: 0, null: false
    t.integer "used_requests", default: 0, null: false
    t.datetime "low_request_warning_sent_at"
    t.datetime "expiry_warning_sent_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["identifier", "end_date"], name: "index_api_request_subscriptions_on_identifier_and_end_date", unique: true
  end

  create_table "assemblies", id: :serial, force: :cascade do |t|
    t.string "sku"
    t.string "title"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug"
    t.string "source"
    t.integer "brand_id"
    t.decimal "price", precision: 12, scale: 4
    t.string "image_url"
    t.jsonb "metadata", default: {}
    t.string "tags"
    t.string "uom"
    t.decimal "pack_weight"
    t.string "pack_description"
    t.decimal "pack_qty"
    t.decimal "pack_cubic_size"
    t.string "objectID"
    t.bigint "shopify_id"
    t.string "alternate_skus_obsolete"
    t.integer "product_type_id"
    t.integer "team_id"
    t.boolean "test_mode", default: false
    t.string "supplier_sku"
    t.string "barcode"
    t.jsonb "stock"
    t.string "related_skus"
    t.string "cloudinary_image_id"
    t.boolean "published", default: true
    t.string "document"
    t.boolean "lock_details", default: false
    t.boolean "clearance", default: false
    t.boolean "grouped", default: false
    t.boolean "always_in_stock", default: false
    t.decimal "length"
    t.decimal "width"
    t.decimal "height"
    t.integer "pack_boxes", default: 1
    t.string "branded_image_url"
    t.string "image_short_url"
    t.decimal "compare_at_price", precision: 8, scale: 4
    t.boolean "override_title", default: false
    t.string "product_code"
    t.integer "search_priority", default: 0
    t.string "catalogue_notes"
    t.boolean "preorder", default: false
    t.boolean "use_retail_charm_pricing", default: true
    t.boolean "allow_dropship", default: false
    t.string "manufacturer_sku"
    t.boolean "dangerous_goods", default: false, null: false
    t.index ["barcode"], name: "index_assemblies_on_barcode", unique: true
    t.index ["brand_id"], name: "index_assemblies_on_brand_id"
    t.index ["product_code"], name: "index_assemblies_on_product_code", unique: true
    t.index ["product_type_id"], name: "index_assemblies_on_product_type_id"
    t.index ["sku"], name: "index_assemblies_on_sku", unique: true
    t.index ["slug"], name: "index_assemblies_on_slug", unique: true
    t.index ["supplier_sku"], name: "index_assemblies_on_supplier_sku", unique: true
    t.index ["team_id"], name: "index_assemblies_on_team_id"
  end

  create_table "assembly_parts", id: :serial, force: :cascade do |t|
    t.integer "assembly_id"
    t.integer "part_id"
    t.decimal "quantity", precision: 8, scale: 4
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assembly_id"], name: "index_assembly_parts_on_assembly_id"
    t.index ["part_id"], name: "index_assembly_parts_on_part_id"
  end

  create_table "attribute_templates", force: :cascade do |t|
    t.bigint "product_type_id", null: false
    t.bigint "attribute_title_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["attribute_title_id"], name: "index_attribute_templates_on_attribute_title_id"
    t.index ["product_type_id"], name: "index_attribute_templates_on_product_type_id"
  end

  create_table "attribute_titles", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "data_type"
    t.integer "position"
    t.boolean "free", default: false
    t.integer "tec_doc_criteria_id"
    t.boolean "vehicle", default: false
    t.boolean "product", default: true
    t.string "tec_doc_title"
    t.integer "tec_doc_max_length"
    t.bigint "parts_db_id"
    t.bigint "merge_with_id"
    t.bigint "team_id"
    t.index ["id"], name: "index_attribute_titles_on_id"
    t.index ["name"], name: "index_attribute_titles_on_name"
    t.index ["parts_db_id"], name: "index_attribute_titles_on_parts_db_id", unique: true
    t.index ["team_id"], name: "index_attribute_titles_on_team_id"
    t.index ["tec_doc_criteria_id"], name: "index_attribute_titles_on_tec_doc_criteria_id", unique: true
  end

  create_table "attribute_values", force: :cascade do |t|
    t.bigint "attribute_title_id"
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.string "abbreviation"
    t.bigint "tecdoc_criteria_id"
    t.string "tecdoc_attribute_key_entry"
    t.index ["attribute_title_id"], name: "index_attribute_values_on_attribute_title_id"
    t.index ["tecdoc_attribute_key_entry"], name: "index_attribute_values_on_tecdoc_attribute_key_entry"
    t.index ["value"], name: "index_attribute_values_on_value"
  end

  create_table "audits", force: :cascade do |t|
    t.integer "auditable_id"
    t.string "auditable_type"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action"
    t.jsonb "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.string "remote_address"
    t.string "request_uuid"
    t.datetime "created_at"
    t.index ["associated_type", "associated_id"], name: "associated_index"
    t.index ["auditable_type", "auditable_id", "version"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "brand_distributors", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "distributor_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_brand_distributors_on_brand_id"
    t.index ["distributor_id"], name: "index_brand_distributors_on_distributor_id"
  end

  create_table "brand_merchants", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "team_id", null: false
    t.boolean "enabled", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_brand_merchants_on_brand_id"
    t.index ["team_id"], name: "index_brand_merchants_on_team_id"
  end

  create_table "brand_seller_agreements", force: :cascade do |t|
    t.bigint "brand_id"
    t.decimal "max_discount_percent", precision: 5, scale: 2
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.text "text"
    t.boolean "active", default: false
    t.index ["brand_id"], name: "index_brand_seller_agreements_on_brand_id"
  end

  create_table "brands", id: :serial, force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "slug"
    t.boolean "test_mode", default: false
    t.string "code"
    t.boolean "update_associations", default: false
    t.string "logo"
    t.string "cloudinary_image_id"
    t.bigint "team_id"
    t.boolean "marketplace", default: false
    t.integer "tec_alliance_id"
    t.boolean "ta_subscription_required", default: false
    t.boolean "tec_alliance_managed", default: false
    t.boolean "require_merchant_application", default: false
    t.datetime "tec_alliance_updated_at"
    t.index ["code"], name: "index_brands_on_code", unique: true
    t.index ["slug", "team_id"], name: "index_brands_on_slug_and_team_id", unique: true
    t.index ["team_id"], name: "index_brands_on_team_id"
  end

  create_table "bulk_import_listings", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.bigint "source_team_id", null: false
    t.bigint "brand_id", null: false
    t.string "sku", null: false
    t.integer "status", default: 0
    t.text "error_message"
    t.datetime "processed_at"
    t.datetime "created_at", precision: 6, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "updated_at", precision: 6, default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.index ["brand_id"], name: "index_bulk_import_listings_on_brand_id"
    t.index ["source_team_id"], name: "index_bulk_import_listings_on_source_team_id"
    t.index ["status"], name: "index_bulk_import_listings_on_status"
    t.index ["team_id", "source_team_id", "brand_id", "sku"], name: "index_bulk_imports_on_team_source_brand_sku"
    t.index ["team_id"], name: "index_bulk_import_listings_on_team_id"
  end

  create_table "collection_brands", id: :serial, force: :cascade do |t|
    t.integer "collection_id"
    t.integer "brand_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_collection_brands_on_brand_id"
    t.index ["collection_id"], name: "index_collection_brands_on_collection_id"
  end

  create_table "collection_links", id: :serial, force: :cascade do |t|
    t.integer "parent_id"
    t.integer "child_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["child_id"], name: "index_collection_links_on_child_id"
    t.index ["parent_id"], name: "index_collection_links_on_parent_id"
  end

  create_table "collection_product_types", id: :serial, force: :cascade do |t|
    t.integer "collection_id"
    t.integer "product_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["collection_id"], name: "index_collection_product_types_on_collection_id"
    t.index ["product_type_id"], name: "index_collection_product_types_on_product_type_id"
  end

  create_table "collection_warehouses", id: :serial, force: :cascade do |t|
    t.integer "collection_id"
    t.integer "distributor_location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["collection_id"], name: "index_collection_warehouses_on_collection_id"
    t.index ["distributor_location_id"], name: "index_collection_warehouses_on_distributor_location_id"
  end

  create_table "collections", id: :serial, force: :cascade do |t|
    t.string "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source"
    t.string "slug"
    t.integer "team_id"
    t.boolean "test_mode", default: false
    t.string "roles_visible"
    t.string "roles_hidden"
    t.integer "position", default: 0
    t.string "filters"
    t.boolean "show_vehicle_search", default: true
    t.string "image_url"
    t.string "cloudinary_image_id"
    t.boolean "featured", default: false
    t.string "vehicle_refinements"
    t.string "attribute_filters"
    t.string "type_filter"
    t.index ["slug"], name: "index_collections_on_slug", unique: true
    t.index ["team_id"], name: "index_collections_on_team_id"
  end

  create_table "cross_sell_products", force: :cascade do |t|
    t.string "productable_type"
    t.bigint "productable_id"
    t.string "related_productable_type"
    t.bigint "related_productable_id"
    t.string "catalogue_notes"
    t.string "relationship_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "product_id"
    t.bigint "related_product_id"
    t.index ["product_id"], name: "index_cross_sell_products_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_csp_product"
    t.index ["related_product_id"], name: "index_cross_sell_products_on_related_product_id"
    t.index ["related_productable_type", "related_productable_id"], name: "index_csp_related_product"
  end

  create_table "debtors", id: :serial, force: :cascade do |t|
    t.string "code"
    t.integer "distributor_location_id"
    t.integer "shopify_integration_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "freight_discount", precision: 10, scale: 2
    t.index ["distributor_location_id"], name: "index_debtors_on_distributor_location_id"
    t.index ["shopify_integration_id"], name: "index_debtors_on_shopify_integration_id"
  end

  create_table "delayed_jobs", id: :serial, force: :cascade do |t|
    t.integer "priority", default: 0, null: false
    t.integer "attempts", default: 0, null: false
    t.text "handler", null: false
    t.text "last_error"
    t.datetime "run_at"
    t.datetime "locked_at"
    t.datetime "failed_at"
    t.string "locked_by"
    t.string "queue"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["priority", "run_at"], name: "delayed_jobs_priority"
  end

  create_table "distributor_accounts", force: :cascade do |t|
    t.bigint "distributor_id", null: false
    t.bigint "team_id", null: false
    t.string "account_code"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "payment_method", default: "credit_card"
    t.bigint "store_id"
    t.boolean "first_party", default: false, null: false
    t.string "shipping_charges_policy", default: "use_fulfillment", null: false
    t.index ["distributor_id"], name: "index_distributor_accounts_on_distributor_id"
    t.index ["store_id"], name: "index_distributor_accounts_on_store_id"
    t.index ["team_id"], name: "index_distributor_accounts_on_team_id"
  end

  create_table "distributor_boxes", force: :cascade do |t|
    t.string "name"
    t.bigint "distributor_id", null: false
    t.decimal "length", precision: 19, scale: 4
    t.decimal "width", precision: 19, scale: 4
    t.decimal "height", precision: 19, scale: 4
    t.decimal "internal_buffer", precision: 19, scale: 4
    t.string "dimensions_unit"
    t.decimal "weight_limit", precision: 19, scale: 4
    t.string "weight_unit"
    t.string "label_size"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "weight", precision: 19, scale: 4
    t.decimal "internal_length", precision: 19, scale: 4
    t.decimal "internal_width", precision: 19, scale: 4
    t.decimal "internal_height", precision: 19, scale: 4
    t.index ["distributor_id"], name: "index_distributor_boxes_on_distributor_id"
  end

  create_table "distributor_connections", force: :cascade do |t|
    t.bigint "distributor_id", null: false
    t.string "type", null: false
    t.string "api_key"
    t.string "api_url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_id"], name: "index_distributor_connections_on_distributor_id"
  end

  create_table "distributor_location_carrier_rates", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.string "carrier_name"
    t.string "country_code"
    t.datetime "valid_from"
    t.datetime "valid_to"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_id"], name: "index_dist_loc_carrier_rates_on_dist_loc_id"
  end

  create_table "distributor_location_users", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_id"], name: "index_distributor_location_users_on_distributor_location_id"
    t.index ["user_id"], name: "index_distributor_location_users_on_user_id"
  end

  create_table "distributor_locations", id: :serial, force: :cascade do |t|
    t.string "code"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "latitude"
    t.float "longitude"
    t.string "location"
    t.string "address"
    t.string "suburb"
    t.integer "postcode"
    t.string "state"
    t.string "country"
    t.bigint "distributor_id"
    t.text "contact_person"
    t.text "contact_email"
    t.text "contact_phone"
    t.text "consignment_email"
    t.boolean "active", default: false
    t.jsonb "pickup_hours"
    t.string "default_shipping_coordinator", default: "distributor"
    t.jsonb "shipping_settings", default: {}
    t.index ["distributor_id", "code"], name: "index_distributor_locations_on_distributor_id_and_code", unique: true
    t.index ["distributor_id"], name: "index_distributor_locations_on_distributor_id"
  end

  create_table "distributor_prices", force: :cascade do |t|
    t.bigint "distributor_id", null: false
    t.bigint "product_id", null: false
    t.bigint "price_cents"
    t.datetime "effective_from"
    t.datetime "effective_to"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_id"], name: "index_distributor_prices_on_distributor_id"
    t.index ["product_id"], name: "index_distributor_prices_on_product_id"
  end

  create_table "distributors", force: :cascade do |t|
    t.string "name"
    t.string "support_email"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "team_id"
    t.string "logo"
    t.string "website"
    t.string "stripe_connect_account"
    t.index ["team_id"], name: "index_distributors_on_team_id", unique: true
  end

  create_table "drafts", id: :serial, force: :cascade do |t|
    t.string "item_type", null: false
    t.integer "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.json "object"
    t.json "previous_draft"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "object_changes"
    t.index ["created_at"], name: "index_drafts_on_created_at"
    t.index ["event"], name: "index_drafts_on_event"
    t.index ["item_id"], name: "index_drafts_on_item_id"
    t.index ["item_type"], name: "index_drafts_on_item_type"
    t.index ["updated_at"], name: "index_drafts_on_updated_at"
    t.index ["whodunnit"], name: "index_drafts_on_whodunnit"
  end

  create_table "export_brands", force: :cascade do |t|
    t.bigint "export_id"
    t.bigint "brand_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_export_brands_on_brand_id"
    t.index ["export_id"], name: "index_export_brands_on_export_id"
  end

  create_table "export_product_types", force: :cascade do |t|
    t.bigint "export_id", null: false
    t.bigint "product_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["export_id"], name: "index_export_product_types_on_export_id"
    t.index ["product_type_id"], name: "index_export_product_types_on_product_type_id"
  end

  create_table "export_template_fields", force: :cascade do |t|
    t.integer "original_column"
    t.string "column_title"
    t.boolean "translatable"
    t.integer "type"
    t.integer "character_limit"
    t.bigint "export_template_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "strip_html", default: false
    t.integer "order"
    t.boolean "uppercase", default: false
    t.boolean "vehicle_specific", default: false
    t.boolean "dangerous_goods", default: false
    t.boolean "bulky_product", default: false
    t.index ["export_template_id"], name: "index_export_template_fields_on_export_template_id"
  end

  create_table "export_templates", force: :cascade do |t|
    t.string "title"
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["team_id"], name: "index_export_templates_on_team_id"
  end

  create_table "exports", force: :cascade do |t|
    t.string "account_code"
    t.string "email"
    t.time "export_time", default: "2000-01-01 08:00:00"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "active", default: false
    t.string "token"
    t.string "latest_url"
    t.boolean "include_flat_rate_shipping", default: false
    t.index ["token"], name: "index_exports_on_token", unique: true
  end

  create_table "external_on_road_vehicles", force: :cascade do |t|
    t.string "vin", null: false
    t.string "plate_number", null: false
    t.string "plate_state", null: false
    t.string "nevdis_make"
    t.string "nevdis_model"
    t.integer "nevdis_year"
    t.string "nevdis_body"
    t.string "nevdis_engine_number"
    t.string "nevdis_compliance_plate"
    t.string "nevdis_colour"
    t.string "factory_make"
    t.string "factory_model"
    t.string "factory_series"
    t.string "factory_variant"
    t.integer "factory_year"
    t.string "factory_body"
    t.string "factory_fuel_type"
    t.string "factory_drive_type"
    t.integer "factory_cylinders"
    t.decimal "factory_engine_capacity", precision: 3, scale: 1
    t.string "factory_transmission"
    t.integer "factory_doors"
    t.integer "factory_seats"
    t.bigint "external_vehicle_source_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_external_on_road_vehicles_on_external_vehicle_source_id"
    t.index ["vin"], name: "index_external_on_road_vehicles_on_vin", unique: true
  end

  create_table "external_vehicle_bodies", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_body_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_external_vehicle_bodies_on_external_vehicle_source_id"
    t.index ["vehicle_body_id"], name: "index_external_vehicle_bodies_on_vehicle_body_id"
  end

  create_table "external_vehicle_drive_types", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_drive_type_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_drive_type_external_vehicle_source"
    t.index ["vehicle_drive_type_id"], name: "index_external_vehicle_drive_types_on_vehicle_drive_type_id"
  end

  create_table "external_vehicle_fuel_types", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_fuel_type_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_fuel_type_external_vehicle_source"
    t.index ["vehicle_fuel_type_id"], name: "index_external_vehicle_fuel_types_on_vehicle_fuel_type_id"
  end

  create_table "external_vehicle_links", force: :cascade do |t|
    t.bigint "vehicle_id", null: false
    t.bigint "external_vehicle_id", null: false
    t.decimal "match_score", precision: 12, scale: 8
    t.boolean "approved"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "use_make", default: false, null: false
    t.boolean "use_model", default: false, null: false
    t.boolean "use_series", default: false, null: false
    t.boolean "use_variant", default: false, null: false
    t.boolean "use_engine", default: false, null: false
    t.boolean "use_body", default: false, null: false
    t.boolean "use_cylinders", default: false, null: false
    t.boolean "use_fuel_type", default: false, null: false
    t.boolean "use_transmission", default: false, null: false
    t.boolean "use_kw_power", default: false, null: false
    t.boolean "use_engine_capacity", default: false, null: false
    t.boolean "use_year_from", default: false, null: false
    t.boolean "use_month_from", default: false, null: false
    t.boolean "use_year_to", default: false, null: false
    t.boolean "use_month_to", default: false, null: false
    t.boolean "use_drive_type", default: false, null: false
    t.index ["external_vehicle_id"], name: "index_external_vehicle_links_on_external_vehicle_id"
    t.index ["vehicle_id", "external_vehicle_id"], name: "index_external_vehicle_links_unique", unique: true
    t.index ["vehicle_id"], name: "index_external_vehicle_links_on_vehicle_id"
  end

  create_table "external_vehicle_makes", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_make_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_external_vehicle_makes_on_external_vehicle_source_id"
    t.index ["vehicle_make_id"], name: "index_external_vehicle_makes_on_vehicle_make_id"
  end

  create_table "external_vehicle_models", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_model_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "external_vehicle_make_id"
    t.index ["external_vehicle_make_id"], name: "index_external_vehicle_models_on_external_vehicle_make_id"
    t.index ["external_vehicle_source_id"], name: "index_external_vehicle_models_on_external_vehicle_source_id"
    t.index ["vehicle_model_id"], name: "index_external_vehicle_models_on_vehicle_model_id"
  end

  create_table "external_vehicle_sources", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
  end

  create_table "external_vehicle_translations", force: :cascade do |t|
    t.integer "external_id"
    t.integer "external_group_id"
    t.bigint "vehicle_model_id"
    t.bigint "vehicle_body_id"
    t.bigint "vehicle_fuel_type_id"
    t.bigint "vehicle_drive_type_id"
    t.bigint "vehicle_transmission_id"
    t.integer "year_from"
    t.integer "month_from"
    t.integer "year_to"
    t.integer "month_to"
    t.string "series"
    t.string "variant"
    t.string "engine"
    t.decimal "engine_capacity", precision: 3, scale: 1
    t.integer "cylinders"
    t.integer "kw_power"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["vehicle_body_id"], name: "index_external_vehicle_translations_on_vehicle_body_id"
    t.index ["vehicle_drive_type_id"], name: "index_external_vehicle_translations_on_vehicle_drive_type_id"
    t.index ["vehicle_fuel_type_id"], name: "index_external_vehicle_translations_on_vehicle_fuel_type_id"
    t.index ["vehicle_model_id"], name: "index_external_vehicle_translations_on_vehicle_model_id"
    t.index ["vehicle_transmission_id"], name: "index_external_vehicle_translations_on_vehicle_transmission_id"
  end

  create_table "external_vehicle_transmissions", force: :cascade do |t|
    t.bigint "external_vehicle_source_id"
    t.bigint "vehicle_transmission_id"
    t.string "name"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["external_vehicle_source_id"], name: "index_transmission_external_vehicle_source"
    t.index ["vehicle_transmission_id"], name: "index_external_vehicle_transmissions_on_vehicle_transmission_id"
  end

  create_table "external_vehicles", force: :cascade do |t|
    t.string "make"
    t.string "model"
    t.string "series"
    t.string "variant"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "external_vehicle_source_id", null: false
    t.string "engine"
    t.string "body"
    t.integer "cylinders"
    t.string "fuel_type"
    t.string "transmission"
    t.integer "kw_power"
    t.decimal "engine_capacity", precision: 12, scale: 4
    t.integer "year_from"
    t.integer "month_from"
    t.integer "year_to"
    t.integer "month_to"
    t.integer "external_id"
    t.integer "external_group_id"
    t.string "drive_type"
    t.bigint "external_vehicle_make_id"
    t.bigint "external_vehicle_model_id"
    t.bigint "external_vehicle_fuel_type_id"
    t.bigint "external_vehicle_drive_type_id"
    t.bigint "external_vehicle_body_id"
    t.bigint "external_vehicle_transmission_id"
    t.index ["external_vehicle_body_id"], name: "index_external_vehicles_on_external_vehicle_body_id"
    t.index ["external_vehicle_drive_type_id"], name: "index_external_vehicles_on_external_vehicle_drive_type_id"
    t.index ["external_vehicle_fuel_type_id"], name: "index_external_vehicles_on_external_vehicle_fuel_type_id"
    t.index ["external_vehicle_make_id"], name: "index_external_vehicles_on_external_vehicle_make_id"
    t.index ["external_vehicle_model_id"], name: "index_external_vehicles_on_external_vehicle_model_id"
    t.index ["external_vehicle_source_id"], name: "index_external_vehicles_on_external_vehicle_source_id"
    t.index ["external_vehicle_transmission_id"], name: "index_external_vehicles_on_external_vehicle_transmission_id"
  end

  create_table "friendly_id_slugs", id: :serial, force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_id"
    t.index ["sluggable_type"], name: "index_friendly_id_slugs_on_sluggable_type"
  end

  create_table "fulfillment_deliveries", force: :cascade do |t|
    t.bigint "fulfillment_id", null: false
    t.string "delivery_method", null: false
    t.string "delivery_status", null: false
    t.string "carrier"
    t.string "carrier_service"
    t.string "carrier_service_id"
    t.string "tracking_number"
    t.string "tracking_url"
    t.string "label_url"
    t.string "label_status"
    t.string "packing_slip_url"
    t.string "pickup_status"
    t.datetime "pickup_window_start"
    t.datetime "pickup_window_finish"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "estimated_cost", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "booked_cost", precision: 10, scale: 2, default: "0.0", null: false
    t.decimal "actual_cost", precision: 10, scale: 2, default: "0.0", null: false
    t.string "pickup_window_id"
    t.string "pickup_id"
    t.string "shipping_coordinator", default: "distributor"
    t.string "shipping_service", default: "manual", null: false
    t.string "shipping_service_id"
    t.index ["fulfillment_id"], name: "index_fulfillment_deliveries_on_fulfillment_id"
  end

  create_table "fulfillment_events", force: :cascade do |t|
    t.bigint "fulfillment_id", null: false
    t.string "event_type"
    t.string "visibility"
    t.string "message"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["fulfillment_id"], name: "index_fulfillment_events_on_fulfillment_id"
  end

  create_table "fulfillment_line_items", force: :cascade do |t|
    t.bigint "merchant_order_line_item_id", null: false
    t.bigint "fulfillment_id", null: false
    t.integer "quantity"
    t.integer "quantity_fulfilled", default: 0
    t.string "distributor_sku"
    t.decimal "unit_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "discount", precision: 10, scale: 2, default: "0.0"
    t.decimal "total_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "total_tax", precision: 10, scale: 2, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["fulfillment_id", "merchant_order_line_item_id"], name: "index_fulfillment_line_items_on_f_id_and_moli_id", unique: true
    t.index ["fulfillment_id"], name: "index_fulfillment_line_items_on_fulfillment_id"
    t.index ["merchant_order_line_item_id"], name: "index_fulfillment_line_items_on_merchant_order_line_item_id"
  end

  create_table "fulfillment_order_transitions", force: :cascade do |t|
    t.string "to_state", null: false
    t.jsonb "metadata", default: {}
    t.integer "sort_key", null: false
    t.integer "fulfillment_order_id", null: false
    t.boolean "most_recent", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["fulfillment_order_id", "most_recent"], name: "index_fulfillment_order_transitions_parent_most_recent", unique: true, where: "most_recent"
    t.index ["fulfillment_order_id", "sort_key"], name: "index_fulfillment_order_transitions_parent_sort", unique: true
  end

  create_table "fulfillment_parcel_items", force: :cascade do |t|
    t.bigint "fulfillment_parcel_id", null: false
    t.integer "quantity", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "fulfillment_unit_id", null: false
    t.index ["fulfillment_parcel_id"], name: "index_fulfillment_parcel_items_on_fulfillment_parcel_id"
    t.index ["fulfillment_unit_id"], name: "index_fulfillment_parcel_items_on_fulfillment_unit_id"
  end

  create_table "fulfillment_parcels", force: :cascade do |t|
    t.decimal "length", precision: 19, scale: 4
    t.decimal "width", precision: 19, scale: 4
    t.decimal "height", precision: 19, scale: 4
    t.string "dimensions_unit"
    t.decimal "weight_limit", precision: 19, scale: 4
    t.string "weight_unit"
    t.bigint "distributor_box_id"
    t.string "label_url"
    t.string "tracking_number"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "weight", precision: 19, scale: 4
    t.bigint "fulfillment_delivery_id", null: false
    t.index ["distributor_box_id"], name: "index_fulfillment_parcels_on_distributor_box_id"
    t.index ["fulfillment_delivery_id"], name: "index_fulfillment_parcels_on_fulfillment_delivery_id"
  end

  create_table "fulfillment_units", force: :cascade do |t|
    t.bigint "fulfillment_line_item_id", null: false
    t.integer "quantity", null: false
    t.decimal "length", precision: 19, scale: 4
    t.decimal "width", precision: 19, scale: 4
    t.decimal "height", precision: 19, scale: 4
    t.decimal "weight", precision: 19, scale: 4
    t.string "identifier"
    t.boolean "ships_separate"
    t.string "dimensions_unit", default: "cm", null: false
    t.string "weight_unit", default: "kg", null: false
    t.index ["fulfillment_line_item_id", "identifier"], name: "index_fulfillment_units_on_line_item_and_identifier", unique: true
    t.index ["fulfillment_line_item_id"], name: "index_fulfillment_units_on_fulfillment_line_item_id"
  end

  create_table "fulfillments", force: :cascade do |t|
    t.bigint "merchant_order_id", null: false
    t.bigint "distributor_location_id", null: false
    t.string "distributor_order_id"
    t.decimal "shipping_charge", precision: 10, scale: 2, default: "0.0"
    t.jsonb "distributor_data", default: {}
    t.string "status"
    t.string "financial_status"
    t.string "distributor_status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "first_party", default: false
    t.string "store_fulfillment_id"
    t.jsonb "store_data", default: {}
    t.string "default_shipping_coordinator", default: "distributor"
    t.decimal "quoted_shipping_charge", precision: 10, scale: 2, default: "0.0"
    t.decimal "quoted_service_charge", precision: 10, scale: 2, default: "0.0"
    t.decimal "quoted_tax", precision: 10, scale: 2, default: "0.0"
    t.decimal "distributor_order_subtotal", precision: 10, scale: 2, default: "0.0"
    t.decimal "distributor_order_charge", precision: 10, scale: 2, default: "0.0"
    t.decimal "distributor_order_discount", precision: 10, scale: 2, default: "0.0"
    t.decimal "distributor_order_tax", precision: 10, scale: 2, default: "0.0"
    t.string "stripe_payment_intent"
    t.string "stripe_payment_status"
    t.string "request_status", default: "pending"
    t.index ["distributor_location_id"], name: "index_fulfillments_on_distributor_location_id"
    t.index ["merchant_order_id", "distributor_location_id"], name: "index_fulfillments_on_mo_id_and_dl_id", unique: true
    t.index ["merchant_order_id"], name: "index_fulfillments_on_merchant_order_id"
  end

  create_table "handover_deliveries", force: :cascade do |t|
    t.bigint "handover_event_id", null: false
    t.bigint "fulfillment_delivery_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["fulfillment_delivery_id"], name: "index_handover_deliveries_on_fulfillment_delivery_id"
    t.index ["handover_event_id"], name: "index_handover_deliveries_on_handover_event_id"
  end

  create_table "handover_events", force: :cascade do |t|
    t.string "handover_type"
    t.bigint "distributor_location_id", null: false
    t.string "status"
    t.string "external_id"
    t.string "organiser"
    t.datetime "preferred_after"
    t.datetime "start"
    t.datetime "end"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "carrier"
    t.string "carrier_service"
    t.string "carrier_service_id"
    t.string "execution_strategy"
    t.jsonb "external_metadata", default: {}
    t.index ["distributor_location_id"], name: "index_handover_events_on_distributor_location_id"
  end

  create_table "import_amazon_prime_listings", force: :cascade do |t|
    t.string "sku"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "validation_error"
  end

  create_table "import_images", force: :cascade do |t|
    t.string "sku"
    t.string "image_url"
    t.bigint "brand_id"
    t.bigint "team_id"
    t.integer "position"
    t.index ["brand_id"], name: "index_import_images_on_brand_id"
    t.index ["team_id"], name: "index_import_images_on_team_id"
  end

  create_table "import_listings", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.bigint "listing_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "title"
    t.string "description"
    t.string "handle"
    t.jsonb "variant_overrides", default: []
    t.string "sku", limit: 25
    t.index ["listing_id"], name: "index_import_listings_on_listing_id"
    t.index ["team_id", "listing_id"], name: "index_import_listings_on_team_id_and_listing_id", unique: true
    t.index ["team_id"], name: "index_import_listings_on_team_id"
  end

  create_table "import_oes", force: :cascade do |t|
    t.string "sku"
    t.string "oe_sku"
    t.string "oem"
    t.boolean "imported", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "import_part_to_assemblies", force: :cascade do |t|
    t.string "product_identifier"
    t.string "assembly_identifier"
    t.integer "quantity", default: 1
    t.bigint "team_id"
    t.datetime "created_at", precision: 6
    t.datetime "updated_at", precision: 6
    t.index ["team_id"], name: "index_import_part_to_assemblies_on_team_id"
  end

  create_table "import_product_types", force: :cascade do |t|
    t.string "sku"
    t.string "product_type_title"
    t.bigint "team_id", null: false
    t.bigint "product_id"
    t.bigint "product_type_id"
    t.index ["product_id"], name: "index_import_product_types_on_product_id"
    t.index ["product_type_id"], name: "index_import_product_types_on_product_type_id"
    t.index ["team_id"], name: "index_import_product_types_on_team_id"
  end

  create_table "import_tecdoc_vehicles", force: :cascade do |t|
    t.integer "k_type", null: false
    t.integer "k_mod", null: false
    t.string "make", null: false
    t.string "model", null: false
    t.string "generation"
    t.string "series"
    t.string "variant"
    t.string "body"
    t.string "engine"
    t.integer "year_from"
    t.integer "month_from"
    t.integer "year_to"
    t.integer "month_to"
    t.string "fuel_type"
    t.decimal "engine_capacity", precision: 3, scale: 1
    t.integer "cylinders"
    t.integer "kw_power"
    t.integer "valves"
    t.string "brake_type"
    t.string "engine_type"
    t.string "drive_type"
    t.string "fuel_mixture_formation"
    t.integer "horse_power"
    t.string "transmission_type"
    t.index ["k_type"], name: "index_import_tecdoc_vehicles_on_k_type", unique: true
  end

  create_table "inventory_imports", force: :cascade do |t|
    t.string "slug"
    t.string "sku"
    t.decimal "barcode"
    t.bigint "distributor_location_id", null: false
    t.decimal "quantity"
    t.bigint "team_id", null: false
    t.bigint "user_id", null: false
    t.bigint "distributor_id", null: false
    t.index ["barcode"], name: "index_inventory_imports_on_barcode"
    t.index ["distributor_id"], name: "index_inventory_imports_on_distributor_id"
    t.index ["distributor_location_id"], name: "index_inventory_imports_on_distributor_location_id"
    t.index ["sku"], name: "index_inventory_imports_on_sku"
    t.index ["slug"], name: "index_inventory_imports_on_slug"
    t.index ["team_id"], name: "index_inventory_imports_on_team_id"
    t.index ["user_id"], name: "index_inventory_imports_on_user_id"
  end

  create_table "listing_variants", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.string "productable_type"
    t.bigint "productable_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "product_id"
    t.string "option_values"
    t.index ["listing_id", "product_id"], name: "index_listing_variants_on_listing_id_and_product_id", unique: true
    t.index ["listing_id"], name: "index_listing_variants_on_listing_id"
    t.index ["product_id"], name: "index_listing_variants_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_listing_variants_on_productable_type_and_productable_id"
  end

  create_table "listings", force: :cascade do |t|
    t.string "title", null: false
    t.string "description", null: false
    t.string "options"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "handle"
    t.boolean "override_auto_handle", default: false
    t.boolean "is_purchasable", default: true
    t.string "status", default: "draft", null: false
    t.bigint "brand_id"
    t.string "sku"
    t.boolean "visible_on_marketplace", default: false
    t.index ["brand_id"], name: "index_listings_on_brand_id"
    t.index ["status"], name: "index_listings_on_status"
  end

  create_table "location_dispatch_freight_rates", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.bigint "product_type_id", null: false
    t.decimal "local_base_rate", precision: 9, scale: 4
    t.decimal "local_multi_qty_discount_percentage", precision: 5, scale: 2
    t.decimal "national_base_rate", precision: 9, scale: 4
    t.decimal "national_multi_qty_discount_percentage", precision: 5, scale: 2
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_id"], name: "dfr_distributor_location_index"
    t.index ["product_type_id"], name: "index_location_dispatch_freight_rates_on_product_type_id"
  end

  create_table "location_dropship_handling_rates", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.decimal "min_rate", precision: 9, scale: 4, default: "0.0"
    t.decimal "percentage_rate", precision: 5, scale: 2, default: "0.0"
    t.decimal "max_rate", precision: 9, scale: 4, default: "0.0"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_id"], name: "dhr_distributor_location_index"
  end

  create_table "location_dropship_product_types", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.bigint "product_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "priority", default: 1, null: false
    t.index ["distributor_location_id"], name: "dpt_distributor_location_index"
    t.index ["product_type_id"], name: "index_location_dropship_product_types_on_product_type_id"
  end

  create_table "maropost_integration_listings", force: :cascade do |t|
    t.string "description"
    t.string "title"
    t.bigint "listing_id", null: false
    t.bigint "maropost_integration_id", null: false
    t.string "maropost_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "sku", limit: 25
    t.index ["listing_id"], name: "index_maropost_integration_listings_on_listing_id"
    t.index ["maropost_integration_id", "listing_id"], name: "index_mil_on_integration_id_and_listing_id", unique: true
    t.index ["maropost_integration_id"], name: "index_maropost_integration_listings_on_maropost_integration_id"
  end

  create_table "maropost_integration_variants", force: :cascade do |t|
    t.bigint "maropost_integration_listing_id", null: false
    t.bigint "listing_variant_id", null: false
    t.string "maropost_id"
    t.string "sku"
    t.decimal "price", precision: 12, scale: 4
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["listing_variant_id"], name: "index_maropost_integration_variants_on_listing_variant_id"
    t.index ["maropost_integration_listing_id", "listing_variant_id"], name: "index_milv_on_integration_listing_id_and_listing_variant_id", unique: true
    t.index ["maropost_integration_listing_id"], name: "index_mil_on_mil_id"
  end

  create_table "maropost_integrations", force: :cascade do |t|
    t.string "access_token"
    t.string "shop"
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.datetime "last_synced_at"
    t.bigint "warehouse_id"
    t.string "warehouse_label"
    t.string "shipping_category"
    t.string "primary_image_allocation"
    t.index ["team_id"], name: "index_maropost_integrations_on_team_id"
  end

  create_table "merchant_order_charges", force: :cascade do |t|
    t.bigint "merchant_order_id", null: false
    t.string "charge_type", default: "shipping", null: false
    t.string "charge_structure", default: "flat", null: false
    t.string "chargeable_type", null: false
    t.bigint "chargeable_id", null: false
    t.string "description", default: "", null: false
    t.string "amount_type", default: "", null: false
    t.decimal "amount", precision: 10, scale: 2, default: "0.0", null: false
    t.string "status", default: "pending", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["charge_type"], name: "index_merchant_order_charges_on_charge_type"
    t.index ["chargeable_type", "chargeable_id"], name: "index_merchant_order_charges_on_chargeable"
    t.index ["merchant_order_id"], name: "index_merchant_order_charges_on_merchant_order_id"
    t.index ["status"], name: "index_merchant_order_charges_on_status"
  end

  create_table "merchant_order_interactions", force: :cascade do |t|
    t.bigint "merchant_order_id", null: false
    t.integer "direction", null: false
    t.string "request", null: false
    t.text "request_message"
    t.string "result"
    t.text "result_message"
    t.integer "status", default: 0, null: false
    t.datetime "interaction_initiated_at", null: false
    t.datetime "interaction_completed_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["merchant_order_id"], name: "index_merchant_order_interactions_on_merchant_order_id"
  end

  create_table "merchant_order_line_items", force: :cascade do |t|
    t.bigint "merchant_order_id", null: false
    t.bigint "product_id", null: false
    t.integer "quantity"
    t.string "partbot_sku"
    t.string "store_sku"
    t.decimal "total_price", precision: 10, scale: 2, default: "0.0"
    t.decimal "total_tax", precision: 10, scale: 2, default: "0.0"
    t.string "fulfillment_status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.decimal "store_unit_price", precision: 10, scale: 2, default: "0.0"
    t.string "store_line_id"
    t.index ["merchant_order_id"], name: "index_merchant_order_line_items_on_merchant_order_id"
    t.index ["product_id"], name: "index_merchant_order_line_items_on_product_id"
  end

  create_table "merchant_orders", force: :cascade do |t|
    t.bigint "store_id"
    t.string "store_order_id"
    t.string "store_order_reference"
    t.string "store_source"
    t.decimal "shipping_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "charges_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "discount_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "subtotal", precision: 10, scale: 2, default: "0.0"
    t.decimal "tax_total", precision: 10, scale: 2, default: "0.0"
    t.decimal "total", precision: 10, scale: 2, default: "0.0"
    t.string "currency"
    t.string "shipping_address1"
    t.string "shipping_address2"
    t.string "shipping_company"
    t.string "shipping_name"
    t.string "shipping_city"
    t.string "shipping_state"
    t.string "shipping_state_code"
    t.string "shipping_postcode"
    t.string "shipping_country"
    t.string "shipping_country_code"
    t.string "delivery_instructions"
    t.jsonb "store_data", default: {}
    t.string "fulfillment_status"
    t.string "financial_status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "shipping_email"
    t.string "shipping_phone"
    t.string "request_status", default: "pending"
    t.index ["store_id"], name: "index_merchant_orders_on_store_id"
  end

  create_table "merchant_product_attributes", force: :cascade do |t|
    t.bigint "merchant_product_id", null: false
    t.bigint "attribute_value_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["attribute_value_id"], name: "index_merchant_product_attributes_on_attribute_value_id"
    t.index ["merchant_product_id", "attribute_value_id"], name: "index_mpa_on_merchant_product_id_and_attribute_value_id", unique: true
    t.index ["merchant_product_id"], name: "index_merchant_product_attributes_on_merchant_product_id"
  end

  create_table "merchant_product_sync_jobs", force: :cascade do |t|
    t.string "job_id"
    t.integer "status"
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["job_id"], name: "index_merchant_product_sync_jobs_on_job_id", unique: true
    t.index ["team_id"], name: "index_merchant_product_sync_jobs_on_team_id"
  end

  create_table "merchant_product_vehicles", force: :cascade do |t|
    t.bigint "merchant_product_id", null: false
    t.bigint "vehicle_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["merchant_product_id", "vehicle_id"], name: "index_mpv_on_merchant_product_id_and_vehicle_id", unique: true
    t.index ["merchant_product_id"], name: "index_merchant_product_vehicles_on_merchant_product_id"
    t.index ["vehicle_id"], name: "index_merchant_product_vehicles_on_vehicle_id"
  end

  create_table "merchant_products", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "store_id"
    t.bigint "store_product_id"
    t.string "title"
    t.string "image_url"
    t.integer "status", default: 0
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "brand"
    t.string "product_type"
    t.string "handle"
    t.jsonb "variants", default: []
    t.string "partbot_id"
    t.string "description"
    t.string "sku"
    t.boolean "published", default: true
    t.boolean "saleable", default: true
    t.string "tags", default: [], array: true
    t.index ["product_id"], name: "index_merchant_products_on_product_id"
    t.index ["store_id"], name: "index_merchant_products_on_store_id"
    t.index ["tags"], name: "index_merchant_products_on_tags", using: :gin
  end

  create_table "oem_references", force: :cascade do |t|
    t.bigint "part_id"
    t.string "reference_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["product_id"], name: "index_oem_references_on_product_id"
  end

  create_table "oem_vehicle_makes", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "vehicle_make_id"
    t.bigint "oem_reference_id"
    t.index ["oem_reference_id"], name: "index_oem_vehicle_makes_on_oem_reference_id"
    t.index ["vehicle_make_id"], name: "index_oem_vehicle_makes_on_vehicle_make_id"
  end

  create_table "package_hazards", force: :cascade do |t|
    t.bigint "package_id", null: false
    t.bigint "product_hazard_id", null: false
    t.decimal "amount", precision: 10, scale: 2
    t.string "amount_unit"
    t.string "label"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["package_id", "product_hazard_id"], name: "index_package_hazards_on_package_id_and_product_hazard_id", unique: true
    t.index ["package_id"], name: "index_package_hazards_on_package_id"
    t.index ["product_hazard_id"], name: "index_package_hazards_on_product_hazard_id"
  end

  create_table "package_imports", force: :cascade do |t|
    t.string "sku", null: false
    t.string "barcode"
    t.bigint "brand_id", null: false
    t.decimal "weight"
    t.string "weight_unit", default: "kg"
    t.decimal "length"
    t.decimal "width"
    t.decimal "height"
    t.string "dimensions_unit", default: "cm"
    t.string "package_type"
    t.string "label"
    t.boolean "dangerous_goods", default: false
    t.boolean "ships_separate", default: false
    t.index ["barcode"], name: "index_package_imports_on_barcode"
    t.index ["brand_id"], name: "index_package_imports_on_brand_id"
    t.index ["sku"], name: "index_package_imports_on_sku"
  end

  create_table "package_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "packages", force: :cascade do |t|
    t.decimal "weight", precision: 19, scale: 4
    t.decimal "length", precision: 19, scale: 4
    t.decimal "width", precision: 19, scale: 4
    t.decimal "height", precision: 19, scale: 4
    t.boolean "visible_labels"
    t.string "productable_type"
    t.bigint "productable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "package_type_id"
    t.bigint "product_id"
    t.bigint "distributor_id"
    t.string "external_reference"
    t.string "dimensions_unit", default: "cm", null: false
    t.string "weight_unit", default: "kg", null: false
    t.boolean "ships_separate", default: false, null: false
    t.string "label"
    t.string "code"
    t.decimal "cubic_weight", precision: 19, scale: 4
    t.uuid "uuid", default: -> { "gen_random_uuid()" }, null: false
    t.index ["distributor_id"], name: "index_packages_on_distributor_id"
    t.index ["package_type_id"], name: "index_packages_on_package_type_id"
    t.index ["product_id", "label"], name: "index_packages_on_product_id_and_label_unique", unique: true
    t.index ["product_id"], name: "index_packages_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_packages_on_productable_type_and_productable_id"
    t.index ["uuid"], name: "index_packages_on_uuid", unique: true
  end

  create_table "partner_submission_products", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.bigint "partner_submission_id", null: false
    t.boolean "active", default: false
    t.datetime "submitted_at"
    t.jsonb "dirty_fields"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["partner_submission_id"], name: "index_partner_submission_products_on_partner_submission_id"
    t.index ["product_id"], name: "index_partner_submission_products_on_product_id"
  end

  create_table "partner_submissions", force: :cascade do |t|
    t.string "company"
    t.string "contact_name"
    t.string "email"
    t.string "format"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "account_code"
    t.bigint "team_id", null: false
    t.index ["team_id"], name: "index_partner_submissions_on_team_id"
  end

  create_table "parts", id: :serial, force: :cascade do |t|
    t.string "sku"
    t.text "description"
    t.string "image_url"
    t.decimal "price", precision: 12, scale: 4
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "metadata", default: {}
    t.string "tags"
    t.string "title"
    t.string "uom"
    t.decimal "pack_weight"
    t.string "pack_description"
    t.decimal "pack_qty"
    t.decimal "pack_cubic_size"
    t.string "objectID"
    t.string "slug"
    t.integer "brand_id"
    t.string "source"
    t.bigint "shopify_id"
    t.string "alternate_skus_obsolete"
    t.integer "product_type_id"
    t.integer "team_id"
    t.boolean "test_mode", default: false
    t.string "supplier_sku"
    t.string "barcode"
    t.jsonb "stock"
    t.string "related_skus"
    t.string "cloudinary_image_id"
    t.boolean "published", default: true
    t.boolean "lock_details", default: false
    t.string "document"
    t.boolean "clearance", default: false
    t.boolean "grouped", default: false
    t.boolean "always_in_stock", default: false
    t.decimal "length"
    t.decimal "width"
    t.decimal "height"
    t.integer "pack_boxes", default: 1
    t.string "branded_image_url"
    t.string "image_short_url"
    t.decimal "compare_at_price", precision: 8, scale: 4
    t.boolean "override_title", default: false
    t.boolean "vehicle_specific", default: false
    t.string "product_code"
    t.integer "search_priority", default: 0
    t.string "catalogue_notes"
    t.boolean "preorder", default: false
    t.boolean "use_retail_charm_pricing", default: true
    t.boolean "allow_dropship", default: false
    t.string "manufacturer_sku"
    t.boolean "dangerous_goods", default: false, null: false
    t.index ["barcode"], name: "index_parts_on_barcode", unique: true
    t.index ["brand_id"], name: "index_parts_on_brand_id"
    t.index ["product_code"], name: "index_parts_on_product_code", unique: true
    t.index ["product_type_id"], name: "index_parts_on_product_type_id"
    t.index ["sku"], name: "index_parts_on_sku", unique: true
    t.index ["slug"], name: "index_parts_on_slug", unique: true
    t.index ["supplier_sku"], name: "index_parts_on_supplier_sku", unique: true
    t.index ["team_id"], name: "index_parts_on_team_id"
  end

  create_table "pricing_imports", force: :cascade do |t|
    t.bigint "barcode"
    t.string "sku"
    t.bigint "price_cents"
    t.datetime "effective_from"
    t.datetime "effective_to"
    t.bigint "distributor_id", null: false
    t.bigint "team_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_id"], name: "index_pricing_imports_on_distributor_id"
    t.index ["team_id"], name: "index_pricing_imports_on_team_id"
    t.index ["user_id"], name: "index_pricing_imports_on_user_id"
  end

  create_table "product_attribute_imports", force: :cascade do |t|
    t.string "sku"
    t.bigint "team_id", null: false
    t.bigint "brand_id", null: false
    t.string "attribute_title"
    t.string "attribute_value"
    t.string "data_type"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_product_attribute_imports_on_brand_id"
    t.index ["team_id"], name: "index_product_attribute_imports_on_team_id"
  end

  create_table "product_attributes", force: :cascade do |t|
    t.string "productable_type"
    t.bigint "productable_id"
    t.bigint "attribute_value_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.integer "sequence"
    t.index ["attribute_value_id"], name: "index_product_attributes_on_attribute_value_id"
    t.index ["product_id"], name: "index_product_attributes_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_product_attributes_on_productable_type_and_productable_id"
  end

  create_table "product_attributes_templates", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.bigint "product_type_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_product_attributes_templates_on_brand_id"
    t.index ["product_type_id"], name: "index_product_attributes_templates_on_product_type_id"
  end

  create_table "product_categories", force: :cascade do |t|
    t.string "title"
    t.integer "shopify_taxonomy_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "ancestry", limit: 8000, collation: "C"
    t.index ["ancestry"], name: "index_product_categories_on_ancestry"
  end

  create_table "product_charts", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.jsonb "chart_data", default: {}
    t.string "title"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "cloudinary_id"
    t.string "url"
    t.index ["product_id"], name: "index_product_charts_on_product_id"
  end

  create_table "product_components", force: :cascade do |t|
    t.bigint "product_id"
    t.bigint "component_product_id"
    t.decimal "quantity", precision: 8, scale: 4
    t.integer "sequence"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "vehicle_primary", default: false
    t.index ["component_product_id"], name: "index_product_components_on_component_product_id"
    t.index ["product_id"], name: "index_product_components_on_product_id"
  end

  create_table "product_gtins", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.string "gtin_type"
    t.string "value"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "active", default: true
    t.bigint "team_id", null: false
    t.index ["product_id"], name: "index_product_gtins_on_product_id"
    t.index ["team_id"], name: "index_product_gtins_on_team_id"
    t.index ["value", "team_id"], name: "index_product_gtins_on_value_and_team_id", unique: true
  end

  create_table "product_hazards", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.bigint "shipping_dangerous_goods_definition_id", null: false
    t.string "supplemental_info"
    t.decimal "amount", precision: 10, scale: 2
    t.string "amount_unit"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["product_id"], name: "index_product_hazards_on_product_id"
    t.index ["shipping_dangerous_goods_definition_id"], name: "index_product_hazards_on_dgd_id"
  end

  create_table "product_images", force: :cascade do |t|
    t.string "productable_type"
    t.bigint "productable_id"
    t.string "cloudinary_id"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["product_id"], name: "index_product_images_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_product_images_on_productable_type_and_productable_id"
  end

  create_table "product_imports", force: :cascade do |t|
    t.string "sku", null: false
    t.string "manufacturer_sku"
    t.decimal "barcode"
    t.bigint "brand_id", null: false
    t.string "title"
    t.string "description"
    t.decimal "price"
    t.string "record_type"
    t.boolean "published"
    t.string "remote_image_url"
    t.string "product_type"
    t.string "component_products"
    t.index ["barcode"], name: "index_product_imports_on_barcode", unique: true
    t.index ["brand_id"], name: "index_product_imports_on_brand_id"
    t.index ["sku", "brand_id"], name: "index_product_imports_on_sku_and_brand_id", unique: true
  end

  create_table "product_issues", force: :cascade do |t|
    t.text "issue"
    t.bigint "reporting_team_id", null: false
    t.bigint "product_id", null: false
    t.boolean "resolved", default: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "user_id", null: false
    t.index ["product_id"], name: "index_product_issues_on_product_id"
    t.index ["reporting_team_id"], name: "index_product_issues_on_reporting_team_id"
    t.index ["user_id"], name: "index_product_issues_on_user_id"
  end

  create_table "product_media", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.string "media_type"
    t.string "cloudinary_id"
    t.string "url"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "position", default: 0, null: false
    t.string "etag"
    t.index ["product_id", "media_type", "position"], name: "index_product_media_on_product_id_and_media_type_and_position", unique: true
    t.index ["product_id", "media_type"], name: "index_product_media_on_product_id_and_media_type"
    t.index ["product_id"], name: "index_product_media_on_product_id"
  end

  create_table "product_models", force: :cascade do |t|
    t.string "name"
    t.bigint "brand_id", null: false
    t.string "slug"
    t.string "logo"
    t.string "cloudinary_image_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_product_models_on_brand_id"
  end

  create_table "product_title_attributes", force: :cascade do |t|
    t.bigint "product_type_id", null: false
    t.bigint "attribute_title_id", null: false
    t.integer "order"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["attribute_title_id"], name: "index_product_title_attributes_on_attribute_title_id"
    t.index ["product_type_id"], name: "index_product_title_attributes_on_product_type_id"
  end

  create_table "product_types", id: :serial, force: :cascade do |t|
    t.string "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "test_mode", default: false
    t.string "code"
    t.boolean "update_associations", default: false
    t.boolean "use_generated_vehicle_titles", default: true
    t.decimal "freight_rate", precision: 10, scale: 2
    t.string "legacy_codes"
    t.bigint "ta_generic_article_id"
    t.string "ta_generic_article_name"
    t.boolean "universal", default: false
    t.bigint "team_id"
    t.index ["ta_generic_article_id"], name: "index_product_types_on_ta_generic_article_id", unique: true
    t.index ["team_id"], name: "index_product_types_on_team_id"
  end

  create_table "product_vehicles_imports", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "sku"
    t.integer "barcode"
    t.string "make"
    t.string "model"
    t.integer "month_from"
    t.integer "year_from"
    t.integer "month_to"
    t.integer "year_to"
    t.string "series"
    t.string "body_type"
    t.boolean "universal_fitment", default: false, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "fitment_notes"
    t.index ["brand_id"], name: "index_product_vehicles_imports_on_brand_id"
  end

  create_table "products", force: :cascade do |t|
    t.citext "sku"
    t.string "title"
    t.text "description"
    t.string "image_url"
    t.decimal "price_deprecated", precision: 12, scale: 4
    t.jsonb "metadata", default: {}
    t.string "tags"
    t.string "uom"
    t.decimal "pack_weight"
    t.string "pack_description"
    t.decimal "pack_qty"
    t.decimal "pack_cubic_size"
    t.string "objectID"
    t.string "slug"
    t.integer "brand_id", null: false
    t.string "source"
    t.bigint "shopify_id"
    t.integer "product_type_id"
    t.integer "team_id"
    t.boolean "test_mode", default: false
    t.string "supplier_sku"
    t.string "barcode"
    t.jsonb "stock"
    t.string "related_skus"
    t.string "cloudinary_image_id"
    t.boolean "published", default: false
    t.boolean "lock_details", default: false
    t.string "document"
    t.boolean "clearance", default: false
    t.boolean "grouped", default: false
    t.boolean "always_in_stock", default: false
    t.decimal "length"
    t.decimal "width"
    t.decimal "height"
    t.integer "pack_boxes", default: 1
    t.string "branded_image_url"
    t.string "image_short_url"
    t.decimal "compare_at_price", precision: 8, scale: 4
    t.boolean "override_title", default: false
    t.boolean "vehicle_specific", default: false
    t.string "product_code"
    t.integer "search_priority", default: 0
    t.string "catalogue_notes"
    t.boolean "preorder", default: false
    t.boolean "use_retail_charm_pricing", default: true
    t.boolean "allow_dropship", default: false
    t.string "manufacturer_sku"
    t.boolean "dangerous_goods", default: false, null: false
    t.string "record_type"
    t.bigint "old_record_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_model_id"
    t.boolean "generic_image", default: false
    t.string "remote_image_url"
    t.bigint "sub_brand_id"
    t.bigint "ta_article_id"
    t.bigint "product_category_id"
    t.datetime "indexed_at"
    t.bigint "price_cents"
    t.boolean "is_oem", default: false
    t.boolean "universal_fitment", default: false
    t.integer "consolidated_length_mm"
    t.integer "consolidated_width_mm"
    t.integer "consolidated_height_mm"
    t.integer "consolidated_weight_g"
    t.boolean "purchaseable", default: true
    t.index ["brand_id"], name: "index_products_on_brand_id"
    t.index ["product_category_id"], name: "index_products_on_product_category_id"
    t.index ["product_code"], name: "index_products_on_product_code", unique: true
    t.index ["product_model_id"], name: "index_products_on_product_model_id"
    t.index ["product_type_id"], name: "index_products_on_product_type_id"
    t.index ["published"], name: "index_products_on_published"
    t.index ["purchaseable"], name: "index_products_on_purchaseable"
    t.index ["sku", "brand_id"], name: "index_products_on_sku_and_brand_id", unique: true
    t.index ["slug"], name: "index_products_on_slug", unique: true
    t.index ["sub_brand_id"], name: "index_products_on_sub_brand_id"
    t.index ["supplier_sku"], name: "index_products_on_supplier_sku", unique: true
    t.index ["ta_article_id"], name: "index_products_on_ta_article_id", unique: true
    t.index ["team_id"], name: "index_products_on_team_id"
  end

  create_table "rate_card_weight_bands", force: :cascade do |t|
    t.bigint "distributor_location_carrier_rate_id", null: false
    t.decimal "max_weight", precision: 10, scale: 2
    t.decimal "price", precision: 10, scale: 2
    t.decimal "surcharge_percent", precision: 10, scale: 2
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_carrier_rate_id"], name: "index_rate_card_wt_bands_on_dist_loc_carrier_rate_id"
  end

  create_table "revenue_share_charges", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.string "stripe_charge_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "price_cents", default: 0
    t.datetime "charged_at"
    t.string "stripe_invoice_id"
    t.string "stripe_invoice_url"
    t.index ["team_id"], name: "index_revenue_share_charges_on_team_id"
  end

  create_table "revenue_share_fulfillment_units", force: :cascade do |t|
    t.bigint "fulfillment_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "revenue_share_charge_id"
    t.integer "price_cents", default: 0
    t.index ["fulfillment_id"], name: "index_revenue_share_fulfillment_units_on_fulfillment_id"
    t.index ["revenue_share_charge_id"], name: "index_rsf_units_on_rsc_id"
  end

  create_table "revenue_share_order_units", force: :cascade do |t|
    t.bigint "store_id", null: false
    t.string "store_order_id"
    t.string "store_order_reference"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "revenue_share_charge_id"
    t.datetime "store_order_date"
    t.integer "price_cents"
    t.bigint "merchant_product_id"
    t.integer "unit_price"
    t.integer "quantity"
    t.index ["merchant_product_id"], name: "index_revenue_share_order_units_on_merchant_product_id"
    t.index ["revenue_share_charge_id"], name: "index_rso_units_on_rsc_id"
    t.index ["store_id"], name: "index_revenue_share_order_units_on_store_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "title"
    t.string "guid"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "sessions", id: :serial, force: :cascade do |t|
    t.string "session_id", null: false
    t.text "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["session_id"], name: "index_sessions_on_session_id", unique: true
    t.index ["updated_at"], name: "index_sessions_on_updated_at"
  end

  create_table "shipment_carton_items", force: :cascade do |t|
    t.bigint "shipment_carton_id", null: false
    t.bigint "shipment_unit_id", null: false
    t.integer "quantity"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["shipment_carton_id"], name: "index_shipment_carton_items_on_shipment_carton_id"
    t.index ["shipment_unit_id"], name: "index_shipment_carton_items_on_shipment_unit_id"
  end

  create_table "shipment_cartons", force: :cascade do |t|
    t.bigint "shipment_id", null: false
    t.string "identifier", null: false
    t.integer "position", null: false
    t.integer "length_mm", default: 0, null: false
    t.integer "width_mm", default: 0, null: false
    t.integer "height_mm", default: 0, null: false
    t.integer "weight_g", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "package_type_id"
    t.index ["package_type_id"], name: "index_shipment_cartons_on_package_type_id"
    t.index ["shipment_id"], name: "index_shipment_cartons_on_shipment_id"
  end

  create_table "shipment_line_items", force: :cascade do |t|
    t.bigint "shipment_id", null: false
    t.bigint "fulfillment_id", null: false
    t.bigint "merchant_order_line_item_id", null: false
    t.integer "quantity"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["fulfillment_id"], name: "index_shipment_line_items_on_fulfillment_id"
    t.index ["merchant_order_line_item_id"], name: "index_shipment_line_items_on_merchant_order_line_item_id"
    t.index ["shipment_id"], name: "index_shipment_line_items_on_shipment_id"
  end

  create_table "shipment_parties", force: :cascade do |t|
    t.bigint "shipment_id", null: false
    t.string "address_type", null: false
    t.string "name"
    t.string "company"
    t.string "email"
    t.string "phone"
    t.string "address1"
    t.string "address2"
    t.string "address3"
    t.string "address4"
    t.string "city"
    t.string "subdivision"
    t.string "subdivision_code"
    t.string "postal_code"
    t.string "country"
    t.string "country_code"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["address_type"], name: "index_shipment_parties_on_address_type"
    t.index ["shipment_id", "address_type"], name: "index_shipment_parties_on_shipment_id_and_address_type", unique: true
    t.index ["shipment_id"], name: "index_shipment_parties_on_shipment_id"
  end

  create_table "shipment_units", force: :cascade do |t|
    t.bigint "shipment_line_item_id", null: false
    t.string "label"
    t.integer "quantity"
    t.integer "length_mm"
    t.integer "width_mm"
    t.integer "height_mm"
    t.integer "weight_g"
    t.string "source", default: "product_package", null: false
    t.string "source_identifier"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["shipment_line_item_id"], name: "index_shipment_units_on_shipment_line_item_id"
  end

  create_table "shipments", force: :cascade do |t|
    t.bigint "distributor_location_id", null: false
    t.string "coordinator"
    t.datetime "shipment_date"
    t.string "status"
    t.jsonb "external_data", default: {}
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "tracking_reference"
    t.string "store_reference"
    t.string "distributor_reference"
    t.string "freight_broker_reference"
    t.string "carrier_name"
    t.string "carrier_tracking_number"
    t.string "carrier_tracking_url"
    t.index ["distributor_location_id"], name: "index_shipments_on_distributor_location_id"
    t.index ["distributor_reference"], name: "index_shipments_on_distributor_reference"
    t.index ["freight_broker_reference"], name: "index_shipments_on_freight_broker_reference"
    t.index ["store_reference"], name: "index_shipments_on_store_reference"
    t.index ["tracking_reference"], name: "index_shipments_on_tracking_reference", unique: true
  end

  create_table "shipping_dangerous_goods_definitions", force: :cascade do |t|
    t.string "un_number", limit: 4, null: false
    t.string "proper_shipping_name", null: false
    t.string "hazard_identifier"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["un_number"], name: "index_shipping_dangerous_goods_definitions_on_un_number"
  end

  create_table "shopify_carrier_services", id: :serial, force: :cascade do |t|
    t.bigint "carrier_service_id"
    t.integer "shopify_integration_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "freight_discount"
    t.datetime "discount_start_date"
    t.datetime "discount_end_date"
    t.string "title"
    t.string "description"
    t.decimal "shipping_charge_cap", precision: 8, scale: 2
    t.index ["shopify_integration_id"], name: "index_shopify_carrier_services_on_shopify_integration_id"
  end

  create_table "shopify_collection_links", force: :cascade do |t|
    t.bigint "shopify_integration_id"
    t.bigint "collection_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["collection_id"], name: "index_shopify_collection_links_on_collection_id"
    t.index ["shopify_integration_id"], name: "index_shopify_collection_links_on_shopify_integration_id"
  end

  create_table "shopify_collections", force: :cascade do |t|
    t.bigint "collection_id"
    t.bigint "shopify_integration_id"
    t.bigint "shopify_collection_id"
    t.index ["collection_id"], name: "index_shopify_collections_on_collection_id"
    t.index ["shopify_integration_id"], name: "index_shopify_collections_on_shopify_integration_id"
  end

  create_table "shopify_fulfillment_services", force: :cascade do |t|
    t.bigint "shopify_integration_id", null: false
    t.bigint "shopify_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "shopify_create_webhook_id"
    t.bigint "shopify_update_webhook_id"
    t.boolean "invoice_charges_and_discounts_from_order", default: false, null: false
    t.boolean "notify_customer"
    t.index ["shopify_integration_id"], name: "index_shopify_fulfillment_services_on_shopify_integration_id"
  end

  create_table "shopify_fulfillments", force: :cascade do |t|
    t.bigint "shopify_id"
    t.bigint "shopify_fulfillment_service_id"
    t.string "status"
    t.string "shopify_order_name"
    t.string "pronto_transaction_id"
    t.bigint "pronto_order_id"
    t.integer "pronto_order_status"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "shopify_order_id"
    t.decimal "fulfillment_subtotal", precision: 10, scale: 2
    t.decimal "fulfillment_tax", precision: 10, scale: 2
    t.string "pronto_consignment_no"
    t.string "pronto_carrier"
    t.decimal "fulfillment_charges", precision: 10, scale: 2
    t.decimal "fulfillment_discount", precision: 10, scale: 2
    t.string "stripe_payment_intent"
    t.string "stripe_payment_status"
    t.index ["shopify_fulfillment_service_id"], name: "index_shopify_fulfillments_on_shopify_fulfillment_service_id"
  end

  create_table "shopify_int_product_types", id: :serial, force: :cascade do |t|
    t.integer "shopify_integration_id"
    t.integer "product_type_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_type_id"], name: "index_shopify_int_product_types_on_product_type_id"
    t.index ["shopify_integration_id"], name: "index_shopify_int_product_types_on_shopify_integration_id"
  end

  create_table "shopify_integration_brands", id: :serial, force: :cascade do |t|
    t.integer "shopify_integration_id"
    t.integer "brand_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_shopify_integration_brands_on_brand_id"
    t.index ["shopify_integration_id"], name: "index_shopify_integration_brands_on_shopify_integration_id"
  end

  create_table "shopify_integration_listings", force: :cascade do |t|
    t.bigint "shopify_integration_id", null: false
    t.bigint "listing_id", null: false
    t.bigint "shopify_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "overwrite_properties", default: true, null: false
    t.boolean "published", default: true, null: false
    t.string "handle"
    t.string "title"
    t.string "description"
    t.index ["listing_id"], name: "index_shopify_integration_listings_on_listing_id"
    t.index ["shopify_integration_id", "listing_id"], name: "index_sil_on_integration_id_and_listing_id", unique: true
    t.index ["shopify_integration_id"], name: "index_shopify_integration_listings_on_shopify_integration_id"
  end

  create_table "shopify_integration_variants", force: :cascade do |t|
    t.bigint "shopify_integration_listing_id", null: false
    t.bigint "listing_variant_id", null: false
    t.bigint "shopify_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "sku"
    t.decimal "price", precision: 12, scale: 4
    t.index ["listing_variant_id"], name: "index_shopify_integration_variants_on_listing_variant_id"
    t.index ["shopify_integration_listing_id", "listing_variant_id"], name: "index_silv_on_integration_listing_id_and_listing_variant_id", unique: true
    t.index ["shopify_integration_listing_id"], name: "index_shopify_listing"
  end

  create_table "shopify_integrations", id: :serial, force: :cascade do |t|
    t.string "shop"
    t.string "access_token"
    t.string "nonce"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "sync_assembly_bom", default: false
    t.integer "team_id"
    t.string "pronto_debtor"
    t.boolean "pause", default: false
    t.boolean "price_rounding", default: false
    t.string "notification_email"
    t.bigint "plan_id"
    t.string "plan_name"
    t.decimal "plan_price"
    t.string "plan_status"
    t.datetime "activated_on"
    t.datetime "billing_on"
    t.datetime "cancelled_on"
    t.integer "trial_days"
    t.datetime "trial_ends_on"
    t.string "billing_cycle"
    t.datetime "current_period_end"
    t.string "stripe_price_id"
    t.string "currency_code", limit: 3, default: "AUD"
    t.string "config_version"
    t.index ["access_token"], name: "index_shopify_integrations_on_access_token", unique: true
    t.index ["nonce"], name: "index_shopify_integrations_on_nonce", unique: true
    t.index ["plan_id"], name: "index_shopify_integrations_on_plan_id", unique: true
    t.index ["team_id"], name: "index_shopify_integrations_on_team_id"
  end

  create_table "shopify_listings", id: :serial, force: :cascade do |t|
    t.integer "shopify_integration_id"
    t.bigint "shopify_id"
    t.integer "part_id"
    t.integer "assembly_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "productable_type"
    t.bigint "productable_id"
    t.string "title"
    t.string "description"
    t.bigint "product_id"
    t.index ["assembly_id"], name: "index_shopify_listings_on_assembly_id"
    t.index ["part_id"], name: "index_shopify_listings_on_part_id"
    t.index ["product_id"], name: "index_shopify_listings_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_shopify_listings_on_productable_type_and_productable_id"
    t.index ["shopify_integration_id"], name: "index_shopify_listings_on_shopify_integration_id"
  end

  create_table "shortened_urls", id: :serial, force: :cascade do |t|
    t.integer "owner_id"
    t.string "owner_type", limit: 20
    t.text "url", null: false
    t.string "unique_key", limit: 10, null: false
    t.string "category"
    t.integer "use_count", default: 0, null: false
    t.datetime "expires_at"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["category"], name: "index_shortened_urls_on_category"
    t.index ["owner_id", "owner_type"], name: "index_shortened_urls_on_owner_id_and_owner_type"
    t.index ["unique_key"], name: "index_shortened_urls_on_unique_key", unique: true
    t.index ["url"], name: "index_shortened_urls_on_url"
  end

  create_table "snapshot_items", force: :cascade do |t|
    t.bigint "snapshot_id", null: false
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.jsonb "object", null: false
    t.datetime "created_at", null: false
    t.string "child_group_name"
    t.index ["item_type", "item_id"], name: "index_snapshot_items_on_item"
    t.index ["snapshot_id", "item_id", "item_type"], name: "index_snapshot_items_on_snapshot_id_and_item_id_and_item_type", unique: true
    t.index ["snapshot_id"], name: "index_snapshot_items_on_snapshot_id"
  end

  create_table "snapshots", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "user_type"
    t.bigint "user_id"
    t.string "identifier"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.index ["identifier", "item_id", "item_type"], name: "index_snapshots_on_identifier_and_item_id_and_item_type", unique: true
    t.index ["identifier"], name: "index_snapshots_on_identifier"
    t.index ["item_type", "item_id"], name: "index_snapshots_on_item"
    t.index ["user_type", "user_id"], name: "index_snapshots_on_user"
  end

  create_table "store_brand_distributors", force: :cascade do |t|
    t.string "store_type", null: false
    t.bigint "store_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "brand_distributor_id", null: false
    t.boolean "preferred"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_distributor_id"], name: "index_store_brand_distributors_on_brand_distributor_id"
    t.index ["brand_id"], name: "index_store_brand_distributors_on_brand_id"
    t.index ["store_id", "store_type", "brand_id", "preferred"], name: "[:store_brand_distributor_pref_index]", unique: true
    t.index ["store_type", "store_id"], name: "index_store_brand_distributors_on_store"
  end

  create_table "store_brand_preferences", force: :cascade do |t|
    t.bigint "store_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "distributor_id", null: false
    t.bigint "distributor_location_id", null: false
    t.integer "sequence", default: 0, null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_store_brand_preferences_on_brand_id"
    t.index ["distributor_id"], name: "index_store_brand_preferences_on_distributor_id"
    t.index ["distributor_location_id"], name: "index_store_brand_preferences_on_distributor_location_id"
    t.index ["store_id"], name: "index_store_brand_preferences_on_store_id"
  end

  create_table "store_distributor_carrier_rates", force: :cascade do |t|
    t.bigint "store_id", null: false
    t.bigint "distributor_location_carrier_rate_id", null: false
    t.boolean "active", default: true
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["distributor_location_carrier_rate_id"], name: "index_store_dist_carrier_rates_on_dist_loc_carrier_rate_id"
    t.index ["store_id"], name: "index_store_distributor_carrier_rates_on_store_id"
  end

  create_table "stores", force: :cascade do |t|
    t.string "integration_type", null: false
    t.bigint "integration_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "team_id"
    t.string "shipping_calculator", default: "default", null: false
    t.boolean "use_partbot_shipping", default: false, null: false
    t.boolean "auto_billing", default: false
    t.string "name"
    t.text "description"
    t.string "website_url"
    t.string "logo_url"
    t.jsonb "theme", default: {}
    t.datetime "last_quantity_sold_updated_at"
    t.string "excluded_revenue_share_platforms", default: [], array: true
    t.index ["integration_type", "integration_id"], name: "index_stores_on_integration"
    t.index ["integration_type", "integration_id"], name: "index_stores_on_integration_type_and_integration_id", unique: true
    t.index ["team_id"], name: "index_stores_on_team_id"
  end

  create_table "sub_brands", force: :cascade do |t|
    t.string "name"
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_sub_brands_on_brand_id"
  end

  create_table "submission_fields", force: :cascade do |t|
    t.bigint "partner_submission_id", null: false
    t.string "title"
    t.string "product_field"
    t.boolean "required", default: false
    t.string "field_type", default: "text"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "sequence"
    t.string "default_value"
    t.integer "limit"
    t.index ["partner_submission_id"], name: "index_submission_fields_on_partner_submission_id"
  end

  create_table "tableflow_import_jobs", force: :cascade do |t|
    t.string "import_id", null: false
    t.string "importer_id"
    t.jsonb "metadata"
    t.integer "num_columns"
    t.integer "num_rows"
    t.integer "num_processed_values"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["import_id"], name: "index_tableflow_import_jobs_on_import_id", unique: true
    t.index ["importer_id"], name: "index_tableflow_import_jobs_on_importer_id"
  end

  create_table "team_domains", force: :cascade do |t|
    t.string "domain"
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.boolean "read_only", default: false
    t.index ["team_id"], name: "index_team_domains_on_team_id"
  end

  create_table "team_members", id: :serial, force: :cascade do |t|
    t.integer "team_id"
    t.integer "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["team_id"], name: "index_team_members_on_team_id"
    t.index ["user_id"], name: "index_team_members_on_user_id"
  end

  create_table "team_plan_logs", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.string "plan", null: false
    t.jsonb "metadata", default: "{}", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["team_id"], name: "index_team_plan_logs_on_team_id"
  end

  create_table "teams", id: :serial, force: :cascade do |t|
    t.string "api_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.string "slug"
    t.string "logo"
    t.string "cloudinary_image_id"
    t.string "stripe_customer_id"
    t.string "plan"
    t.string "email"
    t.boolean "marketplace_platform_enabled", default: true
    t.boolean "catalogue_platform_enabled", default: false
    t.boolean "subscription_active", default: false
    t.boolean "plate_search_active", default: false
    t.string "plate_search_plan"
    t.boolean "eula_accepted", default: false
    t.integer "stores_limit", default: 0
    t.integer "listings_limit", default: 0
    t.boolean "distributor_platform_enabled", default: false
    t.boolean "onboarding_complete", default: false
    t.jsonb "settings"
    t.string "region", limit: 2, default: "AU"
    t.string "legal_business_name"
    t.jsonb "legal_business_ids"
    t.string "timezone", default: "UTC"
    t.string "default_currency_code", limit: 3, default: "AUD"
    t.string "plan_version"
    t.decimal "revenue_share_order_units_discount", precision: 5, scale: 2, default: "0.0"
    t.index ["api_key"], name: "index_teams_on_api_key", unique: true
  end

  create_table "tec_alliance_brand_subscriptions", force: :cascade do |t|
    t.bigint "tec_alliance_merchant_id", null: false
    t.bigint "brand_id", null: false
    t.datetime "billing_start_date"
    t.datetime "next_billing_date"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_tec_alliance_brand_subscriptions_on_brand_id"
    t.index ["tec_alliance_merchant_id"], name: "index_tec_alliance_brand_on_merchant_id"
  end

  create_table "tec_alliance_merchants", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.string "api_key"
    t.datetime "billing_start_date"
    t.datetime "next_billing_date"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "provider_id"
    t.index ["team_id"], name: "index_tec_alliance_merchants_on_team_id"
  end

  create_table "tecdoc_attribute_values", force: :cascade do |t|
    t.bigint "tecdoc_criteria_id"
    t.string "key_entry"
    t.string "description_text"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["tecdoc_criteria_id", "key_entry"], name: "index_ta_av_on_criteria_id_and_key_entry", unique: true
  end

  create_table "tecdoc_attributes", force: :cascade do |t|
    t.bigint "tecdoc_criteria_id"
    t.string "tecdoc_attribute"
    t.string "used_for"
    t.string "data_type"
    t.integer "max_length"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["tecdoc_criteria_id"], name: "index_tecdoc_attributes_on_tecdoc_criteria_id", unique: true
  end

  create_table "template_attributes", force: :cascade do |t|
    t.bigint "product_attributes_template_id", null: false
    t.bigint "attribute_title_id", null: false
    t.integer "sequence"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["attribute_title_id"], name: "index_template_attributes_on_attribute_title_id"
    t.index ["product_attributes_template_id"], name: "index_template_attributes_on_product_attributes_template_id"
  end

  create_table "user_brand_favourites", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "brand_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["brand_id"], name: "index_user_brand_favourites_on_brand_id"
    t.index ["user_id"], name: "index_user_brand_favourites_on_user_id"
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "api_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid", null: false
    t.string "provider", null: false
    t.string "access_token"
    t.string "secret"
    t.string "test_api_key"
    t.integer "team_id"
    t.bigint "role_id"
    t.boolean "write_permission", default: true
    t.boolean "admin", default: false
    t.boolean "editor", default: false
    t.boolean "viewer", default: true
    t.boolean "billing", default: false
    t.boolean "onboarding_intro_complete", default: false
    t.index ["api_key"], name: "index_users_on_api_key", unique: true
    t.index ["provider", "uid"], name: "index_users_on_provider_and_uid", unique: true
    t.index ["provider"], name: "index_users_on_provider"
    t.index ["role_id"], name: "index_users_on_role_id"
    t.index ["team_id"], name: "index_users_on_team_id"
    t.index ["test_api_key"], name: "index_users_on_test_api_key", unique: true
    t.index ["uid"], name: "index_users_on_uid"
  end

  create_table "variants", force: :cascade do |t|
    t.bigint "shopify_listing_id"
    t.string "productable_type"
    t.bigint "productable_id"
    t.bigint "shopify_variant_id"
    t.bigint "product_id"
    t.index ["product_id"], name: "index_variants_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_variants_on_productable_type_and_productable_id"
    t.index ["shopify_listing_id"], name: "index_variants_on_shopify_listing_id"
  end

  create_table "vehicle_application_attributes", force: :cascade do |t|
    t.bigint "vehicle_application_id", null: false
    t.bigint "attribute_value_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["attribute_value_id"], name: "index_vehicle_application_attributes_on_attribute_value_id"
    t.index ["vehicle_application_id"], name: "index_vehicle_application_attributes_on_vehicle_application_id"
  end

  create_table "vehicle_applications", force: :cascade do |t|
    t.string "fitment_notes"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "product_id"
    t.string "document"
    t.string "internal_notes"
    t.string "reference_type"
    t.string "reference"
    t.bigint "merchant_product_id"
    t.index ["merchant_product_id"], name: "index_vehicle_applications_on_merchant_product_id"
    t.index ["product_id"], name: "index_vehicle_applications_on_product_id"
  end

  create_table "vehicle_assemblies", id: :serial, force: :cascade do |t|
    t.integer "vehicle_id"
    t.integer "assembly_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assembly_id"], name: "index_vehicle_assemblies_on_assembly_id"
    t.index ["vehicle_id"], name: "index_vehicle_assemblies_on_vehicle_id"
  end

  create_table "vehicle_attributes", force: :cascade do |t|
    t.string "productable_type"
    t.bigint "productable_id"
    t.bigint "vehicle_id"
    t.bigint "attribute_value_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["attribute_value_id"], name: "index_vehicle_attributes_on_attribute_value_id"
    t.index ["product_id"], name: "index_vehicle_attributes_on_product_id"
    t.index ["productable_type", "productable_id"], name: "index_vehicle_attributes_on_productable_type_and_productable_id"
    t.index ["vehicle_id"], name: "index_vehicle_attributes_on_vehicle_id"
  end

  create_table "vehicle_bodies", force: :cascade do |t|
    t.string "name"
    t.string "abbreviation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vehicle_drive_types", force: :cascade do |t|
    t.string "name"
    t.string "abbreviation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vehicle_fuel_types", force: :cascade do |t|
    t.string "name"
    t.string "abbreviation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vehicle_makes", force: :cascade do |t|
    t.string "name"
    t.string "abbreviation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "popularity", default: 0
    t.string "catalogue_bg_colour"
    t.string "catalogue_txt_colour"
    t.string "catalogue_bg2_colour"
    t.boolean "exclude_from_brand_protection", default: false
  end

  create_table "vehicle_models", force: :cascade do |t|
    t.string "name"
    t.integer "popularity", default: 0
    t.bigint "vehicle_make_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["vehicle_make_id"], name: "index_vehicle_models_on_vehicle_make_id"
  end

  create_table "vehicle_parts", id: :serial, force: :cascade do |t|
    t.integer "vehicle_id"
    t.integer "part_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["part_id"], name: "index_vehicle_parts_on_part_id"
    t.index ["vehicle_id"], name: "index_vehicle_parts_on_vehicle_id"
  end

  create_table "vehicle_products", force: :cascade do |t|
    t.bigint "vehicle_id"
    t.bigint "product_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.bigint "vehicle_application_id"
    t.bigint "merchant_product_id"
    t.index ["merchant_product_id"], name: "index_vehicle_products_on_merchant_product_id"
    t.index ["product_id"], name: "index_vehicle_products_on_product_id"
    t.index ["vehicle_application_id"], name: "index_vehicle_products_on_vehicle_application_id"
    t.index ["vehicle_id"], name: "index_vehicle_products_on_vehicle_id"
  end

  create_table "vehicle_transmissions", force: :cascade do |t|
    t.string "name"
    t.string "abbreviation"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "vehicles", id: :serial, force: :cascade do |t|
    t.string "make"
    t.string "model"
    t.string "series"
    t.string "engine"
    t.string "body"
    t.integer "cylinders"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "k_type"
    t.bigint "epid"
    t.string "variant"
    t.string "note"
    t.string "fuel_type"
    t.integer "year"
    t.string "autoinfo_id"
    t.string "swd_id"
    t.bigint "tecdoc_id"
    t.string "transmission"
    t.string "image_url"
    t.integer "kw_power"
    t.decimal "engine_capacity", precision: 12, scale: 4
    t.boolean "test_mode", default: false
    t.boolean "update_associations", default: false
    t.integer "year_from"
    t.integer "year_to"
    t.bigint "vehicle_make_id"
    t.bigint "vehicle_fuel_type_id"
    t.bigint "vehicle_body_id"
    t.bigint "vehicle_transmission_id"
    t.string "sub_model"
    t.integer "month_from"
    t.integer "month_to"
    t.text "vin"
    t.integer "doors"
    t.bigint "vehicle_drive_type_id"
    t.bigint "vehicle_model_id"
    t.integer "k_mod"
    t.string "status"
    t.integer "draft_id"
    t.datetime "published_at"
    t.datetime "trashed_at"
    t.string "country_of_origin"
    t.string "market", default: "AU", null: false
    t.index ["swd_id"], name: "index_vehicles_on_swd_id", unique: true
    t.index ["vehicle_body_id"], name: "index_vehicles_on_vehicle_body_id"
    t.index ["vehicle_drive_type_id"], name: "index_vehicles_on_vehicle_drive_type_id"
    t.index ["vehicle_fuel_type_id"], name: "index_vehicles_on_vehicle_fuel_type_id"
    t.index ["vehicle_make_id"], name: "index_vehicles_on_vehicle_make_id"
    t.index ["vehicle_model_id"], name: "index_vehicles_on_vehicle_model_id"
    t.index ["vehicle_transmission_id"], name: "index_vehicles_on_vehicle_transmission_id"
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.bigint "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.datetime "created_at"
    t.jsonb "object_changes"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "warehouse_stocks", id: :serial, force: :cascade do |t|
    t.integer "distributor_location_id"
    t.integer "quantity"
    t.string "availability"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["distributor_location_id", "product_id"], name: "index_warehouse_stocks_on_distributor_location_and_product", unique: true
    t.index ["distributor_location_id"], name: "index_warehouse_stocks_on_distributor_location_id"
    t.index ["product_id", "quantity"], name: "index_warehouse_stocks_on_product_id_and_quantity"
    t.index ["product_id"], name: "index_warehouse_stocks_on_product_id"
  end

  create_table "webhook_events", force: :cascade do |t|
    t.string "platform_name"
    t.string "account_id"
    t.string "account_type"
    t.string "event_type"
    t.string "resource_type"
    t.string "resource_id"
    t.jsonb "headers"
    t.jsonb "body"
    t.string "idempotency_key"
    t.string "event_sequence"
    t.datetime "processed_at"
    t.string "status", default: "pending"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["account_id", "resource_type", "resource_id", "status"], name: "index_webhook_events_on_account_resource_status"
    t.index ["idempotency_key", "platform_name", "account_id"], name: "index_webhook_events_on_key_platform_account", unique: true
    t.index ["platform_name", "account_id", "resource_type", "resource_id"], name: "index_webhook_events_on_resource"
    t.index ["processed_at"], name: "index_webhook_events_on_processed_at"
  end

  create_table "woocommerce_integration_listings", force: :cascade do |t|
    t.bigint "woocommerce_integration_id", null: false
    t.bigint "listing_id", null: false
    t.string "woocommerce_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "title"
    t.string "description"
    t.index ["listing_id"], name: "index_woocommerce_integration_listings_on_listing_id"
    t.index ["woocommerce_integration_id", "listing_id"], name: "index_wil_on_integration_id_and_listing_id", unique: true
    t.index ["woocommerce_integration_id"], name: "index_wci_on_wci_id"
  end

  create_table "woocommerce_integration_variants", force: :cascade do |t|
    t.bigint "woocommerce_integration_listing_id", null: false
    t.bigint "listing_variant_id", null: false
    t.string "woocommerce_id"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "woocommerce_image_id"
    t.string "sku"
    t.decimal "price", precision: 12, scale: 4
    t.index ["listing_variant_id"], name: "index_woocommerce_integration_variants_on_listing_variant_id"
    t.index ["woocommerce_integration_listing_id", "listing_variant_id"], name: "index_wilv_on_integration_listing_id_and_listing_variant_id", unique: true
    t.index ["woocommerce_integration_listing_id"], name: "index_wcil_on_wcil_id"
  end

  create_table "woocommerce_integrations", force: :cascade do |t|
    t.string "shop"
    t.string "consumer_key"
    t.string "consumer_secret"
    t.bigint "team_id", null: false
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.string "user_id"
    t.index ["team_id"], name: "index_woocommerce_integrations_on_team_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "alternate_skus", "products"
  add_foreign_key "amazon_listings", "brands"
  add_foreign_key "api_keys", "teams"
  add_foreign_key "api_logs", "users"
  add_foreign_key "assemblies", "brands"
  add_foreign_key "assemblies", "teams"
  add_foreign_key "assembly_parts", "assemblies"
  add_foreign_key "assembly_parts", "parts"
  add_foreign_key "attribute_templates", "attribute_titles"
  add_foreign_key "attribute_templates", "product_types"
  add_foreign_key "attribute_titles", "teams"
  add_foreign_key "attribute_values", "attribute_titles"
  add_foreign_key "brand_distributors", "brands"
  add_foreign_key "brand_distributors", "distributors"
  add_foreign_key "brand_merchants", "brands"
  add_foreign_key "brand_merchants", "teams"
  add_foreign_key "brand_seller_agreements", "brands"
  add_foreign_key "brands", "teams"
  add_foreign_key "bulk_import_listings", "brands"
  add_foreign_key "bulk_import_listings", "teams"
  add_foreign_key "bulk_import_listings", "teams", column: "source_team_id"
  add_foreign_key "collection_brands", "brands"
  add_foreign_key "collection_brands", "collections"
  add_foreign_key "collection_product_types", "collections"
  add_foreign_key "collection_product_types", "product_types"
  add_foreign_key "collection_warehouses", "collections"
  add_foreign_key "collection_warehouses", "distributor_locations"
  add_foreign_key "collections", "teams"
  add_foreign_key "cross_sell_products", "products"
  add_foreign_key "cross_sell_products", "products", column: "related_product_id"
  add_foreign_key "debtors", "distributor_locations"
  add_foreign_key "debtors", "shopify_integrations"
  add_foreign_key "distributor_accounts", "distributors"
  add_foreign_key "distributor_accounts", "stores"
  add_foreign_key "distributor_accounts", "teams"
  add_foreign_key "distributor_boxes", "distributors"
  add_foreign_key "distributor_connections", "distributors"
  add_foreign_key "distributor_location_carrier_rates", "distributor_locations"
  add_foreign_key "distributor_location_users", "distributor_locations"
  add_foreign_key "distributor_location_users", "users"
  add_foreign_key "distributor_locations", "distributors"
  add_foreign_key "distributor_prices", "distributors"
  add_foreign_key "distributor_prices", "products"
  add_foreign_key "distributors", "teams"
  add_foreign_key "export_product_types", "exports"
  add_foreign_key "export_product_types", "product_types"
  add_foreign_key "export_template_fields", "export_templates"
  add_foreign_key "export_templates", "teams"
  add_foreign_key "external_on_road_vehicles", "external_vehicle_sources"
  add_foreign_key "external_vehicle_bodies", "external_vehicle_sources"
  add_foreign_key "external_vehicle_bodies", "vehicle_bodies"
  add_foreign_key "external_vehicle_drive_types", "external_vehicle_sources"
  add_foreign_key "external_vehicle_drive_types", "vehicle_drive_types"
  add_foreign_key "external_vehicle_fuel_types", "external_vehicle_sources"
  add_foreign_key "external_vehicle_fuel_types", "vehicle_fuel_types"
  add_foreign_key "external_vehicle_links", "external_vehicles"
  add_foreign_key "external_vehicle_links", "vehicles"
  add_foreign_key "external_vehicle_makes", "external_vehicle_sources"
  add_foreign_key "external_vehicle_makes", "vehicle_makes"
  add_foreign_key "external_vehicle_models", "external_vehicle_makes"
  add_foreign_key "external_vehicle_models", "external_vehicle_sources"
  add_foreign_key "external_vehicle_models", "vehicle_models"
  add_foreign_key "external_vehicle_translations", "vehicle_bodies"
  add_foreign_key "external_vehicle_translations", "vehicle_drive_types"
  add_foreign_key "external_vehicle_translations", "vehicle_fuel_types"
  add_foreign_key "external_vehicle_translations", "vehicle_models"
  add_foreign_key "external_vehicle_translations", "vehicle_transmissions"
  add_foreign_key "external_vehicle_transmissions", "external_vehicle_sources"
  add_foreign_key "external_vehicle_transmissions", "vehicle_transmissions"
  add_foreign_key "external_vehicles", "external_vehicle_bodies"
  add_foreign_key "external_vehicles", "external_vehicle_drive_types"
  add_foreign_key "external_vehicles", "external_vehicle_fuel_types"
  add_foreign_key "external_vehicles", "external_vehicle_makes"
  add_foreign_key "external_vehicles", "external_vehicle_models"
  add_foreign_key "external_vehicles", "external_vehicle_sources"
  add_foreign_key "external_vehicles", "external_vehicle_transmissions"
  add_foreign_key "fulfillment_deliveries", "fulfillments"
  add_foreign_key "fulfillment_events", "fulfillments"
  add_foreign_key "fulfillment_line_items", "fulfillments"
  add_foreign_key "fulfillment_line_items", "merchant_order_line_items"
  add_foreign_key "fulfillment_order_transitions", "merchant_orders", column: "fulfillment_order_id"
  add_foreign_key "fulfillment_parcel_items", "fulfillment_parcels"
  add_foreign_key "fulfillment_parcel_items", "fulfillment_units"
  add_foreign_key "fulfillment_parcels", "distributor_boxes", on_delete: :nullify
  add_foreign_key "fulfillment_parcels", "fulfillment_deliveries"
  add_foreign_key "fulfillment_units", "fulfillment_line_items"
  add_foreign_key "fulfillments", "distributor_locations"
  add_foreign_key "fulfillments", "merchant_orders"
  add_foreign_key "handover_deliveries", "fulfillment_deliveries"
  add_foreign_key "handover_deliveries", "handover_events"
  add_foreign_key "handover_events", "distributor_locations"
  add_foreign_key "import_images", "brands"
  add_foreign_key "import_images", "teams"
  add_foreign_key "import_listings", "listings"
  add_foreign_key "import_listings", "teams"
  add_foreign_key "import_part_to_assemblies", "teams"
  add_foreign_key "import_product_types", "teams"
  add_foreign_key "inventory_imports", "distributor_locations"
  add_foreign_key "inventory_imports", "distributors"
  add_foreign_key "inventory_imports", "teams"
  add_foreign_key "inventory_imports", "users"
  add_foreign_key "listing_variants", "listings"
  add_foreign_key "listing_variants", "products"
  add_foreign_key "listings", "brands"
  add_foreign_key "location_dispatch_freight_rates", "distributor_locations"
  add_foreign_key "location_dispatch_freight_rates", "product_types"
  add_foreign_key "location_dropship_handling_rates", "distributor_locations"
  add_foreign_key "location_dropship_product_types", "distributor_locations"
  add_foreign_key "location_dropship_product_types", "product_types"
  add_foreign_key "maropost_integration_listings", "listings"
  add_foreign_key "maropost_integration_listings", "maropost_integrations"
  add_foreign_key "maropost_integration_variants", "listing_variants"
  add_foreign_key "maropost_integration_variants", "maropost_integration_listings"
  add_foreign_key "maropost_integrations", "teams"
  add_foreign_key "merchant_order_charges", "merchant_orders"
  add_foreign_key "merchant_order_interactions", "merchant_orders"
  add_foreign_key "merchant_order_line_items", "merchant_orders"
  add_foreign_key "merchant_order_line_items", "products"
  add_foreign_key "merchant_orders", "stores"
  add_foreign_key "merchant_product_attributes", "attribute_values"
  add_foreign_key "merchant_product_attributes", "merchant_products"
  add_foreign_key "merchant_product_sync_jobs", "teams"
  add_foreign_key "merchant_product_vehicles", "merchant_products"
  add_foreign_key "merchant_product_vehicles", "vehicles"
  add_foreign_key "merchant_products", "products"
  add_foreign_key "merchant_products", "stores"
  add_foreign_key "oem_references", "products"
  add_foreign_key "oem_vehicle_makes", "oem_references"
  add_foreign_key "oem_vehicle_makes", "vehicle_makes"
  add_foreign_key "package_hazards", "packages"
  add_foreign_key "package_hazards", "product_hazards"
  add_foreign_key "package_imports", "brands"
  add_foreign_key "packages", "distributors"
  add_foreign_key "packages", "package_types"
  add_foreign_key "packages", "products"
  add_foreign_key "partner_submission_products", "partner_submissions"
  add_foreign_key "partner_submission_products", "products"
  add_foreign_key "partner_submissions", "teams"
  add_foreign_key "parts", "brands"
  add_foreign_key "parts", "teams"
  add_foreign_key "pricing_imports", "distributors"
  add_foreign_key "pricing_imports", "teams"
  add_foreign_key "pricing_imports", "users"
  add_foreign_key "product_attribute_imports", "brands"
  add_foreign_key "product_attribute_imports", "teams"
  add_foreign_key "product_attributes", "attribute_values"
  add_foreign_key "product_attributes", "products"
  add_foreign_key "product_attributes_templates", "brands"
  add_foreign_key "product_attributes_templates", "product_types"
  add_foreign_key "product_charts", "products"
  add_foreign_key "product_components", "products"
  add_foreign_key "product_components", "products", column: "component_product_id"
  add_foreign_key "product_gtins", "products"
  add_foreign_key "product_gtins", "teams"
  add_foreign_key "product_hazards", "products"
  add_foreign_key "product_hazards", "shipping_dangerous_goods_definitions"
  add_foreign_key "product_images", "products"
  add_foreign_key "product_imports", "brands"
  add_foreign_key "product_issues", "products"
  add_foreign_key "product_issues", "teams", column: "reporting_team_id"
  add_foreign_key "product_issues", "users"
  add_foreign_key "product_media", "products"
  add_foreign_key "product_models", "brands"
  add_foreign_key "product_title_attributes", "attribute_titles"
  add_foreign_key "product_title_attributes", "product_types"
  add_foreign_key "product_types", "teams"
  add_foreign_key "product_vehicles_imports", "brands"
  add_foreign_key "products", "product_categories"
  add_foreign_key "products", "product_models"
  add_foreign_key "products", "sub_brands"
  add_foreign_key "rate_card_weight_bands", "distributor_location_carrier_rates"
  add_foreign_key "revenue_share_charges", "teams"
  add_foreign_key "revenue_share_fulfillment_units", "fulfillments"
  add_foreign_key "revenue_share_order_units", "merchant_products"
  add_foreign_key "revenue_share_order_units", "stores"
  add_foreign_key "shipment_carton_items", "shipment_cartons"
  add_foreign_key "shipment_carton_items", "shipment_units"
  add_foreign_key "shipment_cartons", "package_types"
  add_foreign_key "shipment_cartons", "shipments"
  add_foreign_key "shipment_line_items", "fulfillments"
  add_foreign_key "shipment_line_items", "merchant_order_line_items"
  add_foreign_key "shipment_line_items", "shipments"
  add_foreign_key "shipment_parties", "shipments"
  add_foreign_key "shipment_units", "shipment_line_items"
  add_foreign_key "shipments", "distributor_locations"
  add_foreign_key "shopify_carrier_services", "shopify_integrations"
  add_foreign_key "shopify_collection_links", "collections"
  add_foreign_key "shopify_collection_links", "shopify_integrations"
  add_foreign_key "shopify_collections", "collections"
  add_foreign_key "shopify_collections", "shopify_integrations"
  add_foreign_key "shopify_fulfillment_services", "shopify_integrations"
  add_foreign_key "shopify_fulfillments", "shopify_fulfillment_services"
  add_foreign_key "shopify_int_product_types", "product_types"
  add_foreign_key "shopify_int_product_types", "shopify_integrations"
  add_foreign_key "shopify_integration_brands", "brands"
  add_foreign_key "shopify_integration_brands", "shopify_integrations"
  add_foreign_key "shopify_integration_listings", "listings"
  add_foreign_key "shopify_integration_listings", "shopify_integrations"
  add_foreign_key "shopify_integration_variants", "listing_variants"
  add_foreign_key "shopify_integration_variants", "shopify_integration_listings"
  add_foreign_key "shopify_integrations", "teams"
  add_foreign_key "shopify_listings", "assemblies"
  add_foreign_key "shopify_listings", "parts"
  add_foreign_key "shopify_listings", "products"
  add_foreign_key "shopify_listings", "shopify_integrations"
  add_foreign_key "store_brand_distributors", "brand_distributors"
  add_foreign_key "store_brand_distributors", "brands"
  add_foreign_key "store_brand_preferences", "brands"
  add_foreign_key "store_brand_preferences", "distributor_locations"
  add_foreign_key "store_brand_preferences", "distributors"
  add_foreign_key "store_brand_preferences", "stores"
  add_foreign_key "store_distributor_carrier_rates", "distributor_location_carrier_rates"
  add_foreign_key "store_distributor_carrier_rates", "stores"
  add_foreign_key "stores", "teams"
  add_foreign_key "sub_brands", "brands"
  add_foreign_key "submission_fields", "partner_submissions"
  add_foreign_key "team_domains", "teams"
  add_foreign_key "team_members", "teams"
  add_foreign_key "team_members", "users"
  add_foreign_key "team_plan_logs", "teams"
  add_foreign_key "tec_alliance_brand_subscriptions", "brands"
  add_foreign_key "tec_alliance_brand_subscriptions", "tec_alliance_merchants"
  add_foreign_key "tec_alliance_merchants", "teams"
  add_foreign_key "tecdoc_attribute_values", "tecdoc_attributes", column: "tecdoc_criteria_id", primary_key: "tecdoc_criteria_id"
  add_foreign_key "template_attributes", "attribute_titles"
  add_foreign_key "template_attributes", "product_attributes_templates"
  add_foreign_key "user_brand_favourites", "brands"
  add_foreign_key "user_brand_favourites", "users"
  add_foreign_key "users", "roles"
  add_foreign_key "users", "teams"
  add_foreign_key "variants", "products"
  add_foreign_key "variants", "shopify_listings"
  add_foreign_key "vehicle_application_attributes", "attribute_values"
  add_foreign_key "vehicle_application_attributes", "vehicle_applications"
  add_foreign_key "vehicle_applications", "merchant_products"
  add_foreign_key "vehicle_applications", "products"
  add_foreign_key "vehicle_assemblies", "assemblies"
  add_foreign_key "vehicle_assemblies", "vehicles"
  add_foreign_key "vehicle_attributes", "attribute_values"
  add_foreign_key "vehicle_attributes", "products"
  add_foreign_key "vehicle_attributes", "vehicles"
  add_foreign_key "vehicle_models", "vehicle_makes"
  add_foreign_key "vehicle_parts", "parts"
  add_foreign_key "vehicle_parts", "vehicles"
  add_foreign_key "vehicle_products", "merchant_products"
  add_foreign_key "vehicle_products", "products"
  add_foreign_key "vehicle_products", "vehicle_applications"
  add_foreign_key "vehicle_products", "vehicles"
  add_foreign_key "vehicles", "vehicle_bodies"
  add_foreign_key "vehicles", "vehicle_drive_types"
  add_foreign_key "vehicles", "vehicle_fuel_types"
  add_foreign_key "vehicles", "vehicle_makes"
  add_foreign_key "vehicles", "vehicle_models"
  add_foreign_key "vehicles", "vehicle_transmissions"
  add_foreign_key "warehouse_stocks", "distributor_locations"
  add_foreign_key "warehouse_stocks", "products"
  add_foreign_key "woocommerce_integration_listings", "listings"
  add_foreign_key "woocommerce_integration_listings", "woocommerce_integrations"
  add_foreign_key "woocommerce_integration_variants", "listing_variants"
  add_foreign_key "woocommerce_integration_variants", "woocommerce_integration_listings"
  add_foreign_key "woocommerce_integrations", "teams"

  create_view "external_vehicle_views", sql_definition: <<-SQL
      SELECT ev.id,
      COALESCE(g_mk.name, mk.name) AS make,
      COALESCE(m_md.name, g_md.name, md.name) AS model,
      COALESCE(vt.series, mt.series, ev.series) AS series,
      COALESCE(v_bd.name, m_bd.name, g_bd.name, bd.name) AS body,
      COALESCE(vt.month_from, ev.month_from) AS month_from,
      COALESCE(vt.year_from, ev.year_from) AS year_from,
      COALESCE(vt.month_to, ev.month_to) AS month_to,
      COALESCE(vt.year_to, ev.year_to) AS year_to,
      COALESCE(vt.variant, ev.variant) AS variant,
      COALESCE(vt.engine, ev.engine) AS engine,
      (COALESCE(vt.engine_capacity, ev.engine_capacity))::numeric(12,4) AS engine_capacity,
      COALESCE(vt.cylinders, ev.cylinders) AS cylinders,
      COALESCE(vt.kw_power, ev.kw_power) AS kw_power,
      COALESCE(v_ft.name, m_ft.name, g_ft.name, ft.name) AS fuel_type,
      COALESCE(v_dt.name, m_dt.name, g_dt.name, dt.name) AS drive_type,
      COALESCE(v_tr.name, m_tr.name, g_tr.name, tr.name) AS transmission,
      sc.name AS external_source,
      ev.external_id,
      ev.external_group_id,
      (g_mk.name IS NOT NULL) AS global_make_translated,
      (g_md.name IS NOT NULL) AS global_model_translated,
      (g_bd.name IS NOT NULL) AS global_body_translated,
      (g_ft.name IS NOT NULL) AS global_fuel_type_translated,
      (g_dt.name IS NOT NULL) AS global_drive_type_translated,
      (g_tr.name IS NOT NULL) AS global_transmission_translated,
      (m_md.name IS NOT NULL) AS model_translated,
      (COALESCE(vt.series, mt.series) IS NOT NULL) AS series_translated,
      (COALESCE(v_bd.name, m_bd.name) IS NOT NULL) AS body_translated,
      (vt.month_from IS NOT NULL) AS month_from_translated,
      (vt.year_from IS NOT NULL) AS year_from_translated,
      (vt.month_to IS NOT NULL) AS month_to_translated,
      (vt.year_to IS NOT NULL) AS year_to_translated,
      (vt.variant IS NOT NULL) AS variant_translated,
      (vt.engine IS NOT NULL) AS engine_translated,
      (vt.engine_capacity IS NOT NULL) AS engine_capacity_translated,
      (vt.cylinders IS NOT NULL) AS cylinders_translated,
      (vt.kw_power IS NOT NULL) AS kw_power_translated,
      (COALESCE(v_ft.name, m_ft.name) IS NOT NULL) AS fuel_type_translated,
      (COALESCE(v_dt.name, m_dt.name) IS NOT NULL) AS drive_type_translated,
      (COALESCE(v_tr.name, m_tr.name) IS NOT NULL) AS transmission_translated
     FROM ((((((((((((((((((((((((external_vehicles ev
       JOIN external_vehicle_makes mk ON ((mk.id = ev.external_vehicle_make_id)))
       JOIN external_vehicle_models md ON ((md.id = ev.external_vehicle_model_id)))
       JOIN external_vehicle_bodies bd ON ((bd.id = ev.external_vehicle_body_id)))
       JOIN external_vehicle_fuel_types ft ON ((ft.id = ev.external_vehicle_fuel_type_id)))
       JOIN external_vehicle_drive_types dt ON ((dt.id = ev.external_vehicle_drive_type_id)))
       JOIN external_vehicle_transmissions tr ON ((tr.id = ev.external_vehicle_transmission_id)))
       JOIN external_vehicle_sources sc ON ((sc.id = ev.external_vehicle_source_id)))
       LEFT JOIN vehicle_makes g_mk ON ((g_mk.id = mk.vehicle_make_id)))
       LEFT JOIN vehicle_models g_md ON ((g_md.id = md.vehicle_model_id)))
       LEFT JOIN vehicle_bodies g_bd ON ((g_bd.id = bd.vehicle_body_id)))
       LEFT JOIN vehicle_fuel_types g_ft ON ((g_ft.id = ft.vehicle_fuel_type_id)))
       LEFT JOIN vehicle_drive_types g_dt ON ((g_dt.id = dt.vehicle_drive_type_id)))
       LEFT JOIN vehicle_transmissions g_tr ON ((g_tr.id = tr.vehicle_transmission_id)))
       LEFT JOIN external_vehicle_translations mt ON ((mt.external_group_id = ev.external_group_id)))
       LEFT JOIN vehicle_models m_md ON ((m_md.id = mt.vehicle_model_id)))
       LEFT JOIN vehicle_bodies m_bd ON ((m_bd.id = mt.vehicle_body_id)))
       LEFT JOIN vehicle_fuel_types m_ft ON ((m_ft.id = mt.vehicle_fuel_type_id)))
       LEFT JOIN vehicle_drive_types m_dt ON ((m_dt.id = mt.vehicle_drive_type_id)))
       LEFT JOIN vehicle_transmissions m_tr ON ((m_tr.id = mt.vehicle_transmission_id)))
       LEFT JOIN external_vehicle_translations vt ON ((vt.external_id = ev.external_id)))
       LEFT JOIN vehicle_bodies v_bd ON ((v_bd.id = vt.vehicle_body_id)))
       LEFT JOIN vehicle_fuel_types v_ft ON ((v_ft.id = vt.vehicle_fuel_type_id)))
       LEFT JOIN vehicle_drive_types v_dt ON ((v_dt.id = vt.vehicle_drive_type_id)))
       LEFT JOIN vehicle_transmissions v_tr ON ((v_tr.id = vt.vehicle_transmission_id)));
  SQL
  create_view "vehicle_views", sql_definition: <<-SQL
      SELECT vehicles.id,
      vehicle_makes.name AS make,
      vehicle_models.name AS model,
      vehicle_bodies.name AS body,
      vehicle_fuel_types.name AS fuel_type,
      vehicle_drive_types.name AS drive_type,
      vehicle_transmissions.name AS transmission,
      vehicles.series,
      vehicles.variant,
      vehicles.year_from,
      vehicles.month_from,
      vehicles.year_to,
      vehicles.month_to,
      vehicles.engine,
      vehicles.engine_capacity,
      vehicles.cylinders,
      vehicles.doors,
      vehicles.kw_power,
      vehicles.tecdoc_id,
      vehicles.autoinfo_id,
      vehicles.k_mod,
      vehicles.swd_id,
          CASE
              WHEN (NULLIF(vehicles.tecdoc_id, 0) IS NOT NULL) THEN 'TecDoc'::text
              WHEN (NULLIF((vehicles.autoinfo_id)::text, ''::text) IS NOT NULL) THEN 'OSCAR'::text
              ELSE 'Partbot'::text
          END AS source,
      COALESCE(vp.product_count, (0)::bigint) AS product_count,
      vehicles.created_at,
      vehicles.updated_at,
      vehicles.status,
      (vehicles.created_at > (now() - 'P3M'::interval)) AS new_vehicle,
      (vehicles.updated_at > (now() - 'P3M'::interval)) AS updated_vehicle
     FROM (((((((vehicles
       LEFT JOIN vehicle_makes ON ((vehicle_makes.id = vehicles.vehicle_make_id)))
       LEFT JOIN vehicle_models ON ((vehicle_models.id = vehicles.vehicle_model_id)))
       LEFT JOIN vehicle_bodies ON ((vehicle_bodies.id = vehicles.vehicle_body_id)))
       LEFT JOIN vehicle_fuel_types ON ((vehicle_fuel_types.id = vehicles.vehicle_fuel_type_id)))
       LEFT JOIN vehicle_drive_types ON ((vehicle_drive_types.id = vehicles.vehicle_drive_type_id)))
       LEFT JOIN vehicle_transmissions ON ((vehicle_transmissions.id = vehicles.vehicle_transmission_id)))
       LEFT JOIN ( SELECT vp_1.vehicle_id,
              count(*) AS product_count
             FROM vehicle_products vp_1
            GROUP BY vp_1.vehicle_id) vp ON ((vp.vehicle_id = vehicles.id)));
  SQL
end
