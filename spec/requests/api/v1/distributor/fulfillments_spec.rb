require "swagger_helper"

RSpec.describe "Distributor API - Fulfillments", type: :request do
  path "/fulfillments" do
    get("List Fulfillments") do
      tags "Fulfillments"
      description "List all fulfillments for a location"
      produces "application/json"
      operationId "fulfillmentsList"

      parameter "$ref" => "#/components/parameters/location_ids"
      parameter "$ref" => "#/components/parameters/page"
      parameter "$ref" => "#/components/parameters/page_size"

      response(200, "successful") do
        schema type: :array, items: { "$ref" => "#/components/schemas/Fulfillment" }

        let(:location_id) { "123" }

        after do |example|
          example.metadata[:response][:content] = {
            "application/json" => {
              example: JSON.parse(response.body, symbolize_names: true)
            }
          }
        end
        run_test!
      end
    end
  end

  path "/fulfillments/statistics" do
    get("Get Fulfillment Statistics") do
      tags "Fulfillments"
      description "Get statistics for fulfillments by location IDs"
      produces "application/json"
      operationId "fulfillmentsStatistics"

      parameter "$ref" => "#/components/parameters/location_ids"

      response(200, "successful") do
        schema type: :array, items: { type: :object }

        let(:location_ids) { "4,8,6,3" }

        # after do |example|
        #   example.metadata[:response][:content] = {
        #     "application/json" => {
        #       example: JSON.parse(response.body, symbolize_names: true)
        #     }
        #   }
        # end
        run_test!
      end
    end
  end

  path "/fulfillments/{id}" do
    parameter name: "id", in: :path, type: :integer, description: "Fulfillment ID"

    get("Get Fulfillment") do
      tags "Fulfillments"
      description "Get fulfillment by ID for a location"
      produces "application/json"
      operationId "fulfillmentById"

      response(200, "successful") do
        schema "$ref" => "#/components/schemas/Fulfillment"
        let(:location_id) { "123" }
        let(:id) { "123" }

        after do |example|
          example.metadata[:response][:content] = {
            "application/json" => {
              example: JSON.parse(response.body, symbolize_names: true)
            }
          }
        end
        run_test!
      end
    end
  end

  path "/fulfillments/{id}/fulfillment_request" do
    parameter name: :id, in: :path, type: :string, description: "Fulfillment ID"

    put("Update Fulfillment Request") do
      tags "Fulfillments"
      description "Accept or reject a fulfillment request for a fulfillment"
      consumes "application/json"
      produces "application/json"
      operationId "fulfillmentRequestUpdate"

      parameter name: :fulfillment_request,
                in: :body,
                required: true,
                description: "fulfillment request parameters",
                schema: {
                  type: :object,
                  properties: {
                    status: {
                      type: :string,
                      enum: %w[ACCEPTED REJECTED]
                    }
                  }
                }

      response(200, "successful") do
        schema type: :object, properties: { message: { type: :string } }

        let(:id) { "123" }
        let(:fulfillment_request) { { status: "ACCEPTED" } }

        # after do |example|
        #   example.metadata[:response][:content] = {
        #     "application/json" => {
        #       example: JSON.parse(response.body, symbolize_names: true)
        #     }
        #   }
        # end
        run_test!
      end
    end
  end

  path "/fulfillments/{id}/cancellation_request" do
    parameter name: :id, in: :path, type: :string, description: "Fulfillment ID"

    put("Update Cancellation Request") do
      tags "Fulfillments"
      description "Accept or reject a cancellation request for a fulfillment"
      consumes "application/json"
      produces "application/json"
      operationId "cancellationRequestUpdate"

      parameter name: :cancellation_request,
                in: :body,
                required: true,
                description: "cancellation request parameters",
                schema: {
                  type: :object,
                  properties: {
                    status: {
                      type: :string,
                      enum: %w[ACCEPTED REJECTED]
                    }
                  }
                }

      response(200, "successful") do
        schema type: :object, properties: { message: { type: :string } }

        let(:id) { "123" }
        let(:cancellation_request) { { status: "ACCEPTED" } }

        # after do |example|
        #   example.metadata[:response][:content] = {
        #     "application/json" => {
        #       example: JSON.parse(response.body, symbolize_names: true)
        #     }
        #   }
        # end
        run_test!
      end
    end
  end

  path "/fulfillments/{id}/change_location" do
    parameter name: :id, in: :path, type: :string, description: "Fulfillment ID"

    put("Change Fulfillment Location") do
      tags "Fulfillments"
      description "Change the location for a fulfillment"
      consumes "application/json"
      produces "application/json"
      operationId "changeLocationUpdate"

      parameter name: :change_location,
                in: :body,
                required: true,
                description: "change location parameters",
                schema: {
                  type: :object,
                  properties: {
                    new_location_id: {
                      type: :integer,
                      format: :int64
                    }
                  },
                  required: [:new_location_id]
                }

      response(200, "successful") do
        schema "$ref" => "#/components/schemas/Fulfillment"

        let(:id) { "123" }
        let(:change_location) { { new_location_id: 456 } }

        # after do |example|
        #   example.metadata[:response][:content] = {
        #     "application/json" => {
        #       example: JSON.parse(response.body, symbolize_names: true)
        #     }
        #   }
        # end
        run_test!
      end

      response(422, "unprocessable entity") do
        schema type: :object, properties: { error: { type: :string } }

        let(:id) { "123" }
        let(:change_location) { { new_location_id: 456 } }

        run_test!
      end
    end
  end
end
