---
openapi: 3.0.1
info:
  title: Partbot Distributor API
  version: v1
paths:
  "/deliveries":
    get:
      summary: List Deliveries
      tags:
      - Deliveries
      description: List all deliveries for one or more locations
      operationId: deliveriesList
      parameters:
      - "$ref": "#/components/parameters/location_ids"
      - "$ref": "#/components/parameters/page"
      - "$ref": "#/components/parameters/page_size"
      - name: type
        in: query
        description: Type of delivery
        schema:
          type: string
          enum:
          - SHIPPED
          - UNSHIPPED
          default: UNSHIPPED
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/Deliveries"
  "/deliveries/{id}":
    parameters:
    - name: id
      in: path
      description: Delivery ID
      schema:
        type: integer
        format: int64
        minimum: 1
      required: true
    delete:
      summary: Cancel Delivery
      tags:
      - Deliveries
      description: Cancel a delivery
      operationId: deliveriesCancel
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  "/deliveries/pickup":
    post:
      summary: Create Deliveries Pickup
      tags:
      - Deliveries
      description: Create a delivery pickup
      operationId: deliveriesPickup
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                booking_ids:
                  type: array
                  items:
                    type: string
                pickup_date:
                  type: string
                  format: date
        required: true
        description: manifest parameters
  "/deliveries/{id}/documents":
    parameters:
    - name: id
      in: path
      description: Delivery ID
      schema:
        type: integer
        format: int64
        minimum: 1
      required: true
    get:
      summary: Get Delivery Document
      tags:
      - Deliveries
      description: Get a delivery document
      operationId: deliveryDocument
      parameters:
      - name: document_type
        in: query
        description: Type of document
        required: true
        schema:
          type: string
          enum:
          - SHIPPING_LABEL
          - SHIPPING_CONNOTE
          - SHIPPING_MANIFEST
          - SHIPPING_COMMERCIAL_INVOICE
          - SHIPPING_DG_DOCUMENT
          - POD
          default: SHIPPING_LABEL
      responses:
        '200':
          description: successful
          content:
            application/pdf:
              schema:
                type: string
                format: binary
  "/fulfillments":
    get:
      summary: List Fulfillments
      tags:
      - Fulfillments
      description: List all fulfillments for a location
      operationId: fulfillmentsList
      parameters:
      - "$ref": "#/components/parameters/location_ids"
      - "$ref": "#/components/parameters/page"
      - "$ref": "#/components/parameters/page_size"
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/Fulfillment"
  "/fulfillments/statistics":
    get:
      summary: Get Fulfillment Statistics
      tags:
      - Fulfillments
      description: Get statistics for fulfillments by location IDs
      operationId: fulfillmentsStatistics
      parameters:
      - "$ref": "#/components/parameters/location_ids"
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  "/fulfillments/{id}":
    parameters:
    - name: id
      in: path
      description: Fulfillment ID
      required: true
      schema:
        type: integer
    get:
      summary: Get Fulfillment
      tags:
      - Fulfillments
      description: Get fulfillment by ID for a location
      operationId: fulfillmentById
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Fulfillment"
  "/fulfillments/{id}/fulfillment_request":
    parameters:
    - name: id
      in: path
      description: Fulfillment ID
      required: true
      schema:
        type: string
    put:
      summary: Update Fulfillment Request
      tags:
      - Fulfillments
      description: Accept or reject a fulfillment request for a fulfillment
      operationId: fulfillmentRequestUpdate
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                  - ACCEPTED
                  - REJECTED
        required: true
        description: fulfillment request parameters
  "/fulfillments/{id}/cancellation_request":
    parameters:
    - name: id
      in: path
      description: Fulfillment ID
      required: true
      schema:
        type: string
    put:
      summary: Update Cancellation Request
      tags:
      - Fulfillments
      description: Accept or reject a cancellation request for a fulfillment
      operationId: cancellationRequestUpdate
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                  - ACCEPTED
                  - REJECTED
        required: true
        description: cancellation request parameters
  "/fulfillments/{id}/change_location":
    parameters:
    - name: id
      in: path
      description: Fulfillment ID
      required: true
      schema:
        type: string
    put:
      summary: Change Fulfillment Location
      tags:
      - Fulfillments
      description: Change the location for a fulfillment
      operationId: changeLocationUpdate
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Fulfillment"
        '422':
          description: unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                new_location_id:
                  type: integer
                  format: int64
              required:
              - new_location_id
        required: true
        description: change location parameters
  "/locations":
    get:
      summary: List Locations
      tags:
      - Locations
      description: List all distributor locations
      operationId: locationsList
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/DistributorLocation"
        '401':
          description: Unauthorized
  "/locations/{id}":
    parameters:
    - name: id
      in: path
      description: Location ID
      required: true
      schema:
        type: string
    get:
      summary: Get Location
      tags:
      - Locations
      description: Get distributor location by ID
      operationId: locationById
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DistributorLocation"
  "/shipments/{id}/units":
    parameters:
    - name: id
      in: path
      description: Shipment ID
      required: true
      schema:
        "$ref": "#/components/schemas/ID"
    post:
      summary: Create Shipment Unit
      tags:
      - Shipments
      description: Add a new unit to a shipment line item
      operationId: shipmentsAddUnit
      parameters: []
      responses:
        '201':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Shipment"
        '404':
          description: not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '422':
          description: unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - line_item_id
              - quantity
              - length_mm
              - width_mm
              - height_mm
              - weight_g
              properties:
                line_item_id:
                  "$ref": "#/components/schemas/ID"
                quantity:
                  type: integer
                  example: 1
                length_mm:
                  type: integer
                  example: 200
                width_mm:
                  type: integer
                  example: 150
                height_mm:
                  type: integer
                  example: 100
                weight_g:
                  type: integer
                  example: 500
                source:
                  type: string
                  example: manual
                source_identifier:
                  type: string
                  example: SKU123
                label:
                  type: string
                  example: Fragile Item
                metadata:
                  type: object
                  properties:
                    packaging:
                      type: object
                      properties:
                        ships_separate:
                          type: boolean
                          example: false
        required: true
        description: Unit parameters
  "/shipments/{shipment_id}/units/{id}":
    parameters:
    - name: shipment_id
      in: path
      description: Shipment ID
      required: true
      schema:
        "$ref": "#/components/schemas/ID"
    - name: id
      in: path
      description: Unit ID
      required: true
      schema:
        "$ref": "#/components/schemas/ID"
    delete:
      summary: Delete Shipment Unit
      tags:
      - Shipments
      description: Remove a unit from a shipment
      operationId: shipmentsRemoveUnit
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Shipment"
        '404':
          description: not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
        '422':
          description: unprocessable entity
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: string
  "/shipments":
    get:
      summary: List Shipments
      tags:
      - Shipments
      description: List all shipments for a location
      operationId: shipmentsList
      parameters:
      - "$ref": "#/components/parameters/location_ids"
      - "$ref": "#/components/parameters/page"
      - "$ref": "#/components/parameters/page_size"
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/Shipments"
  "/shipments/statistics":
    get:
      summary: Get Shipments Statistics
      tags:
      - Shipments
      description: Get statistics for shipments by location IDs
      operationId: shipmentsStatistics
      parameters:
      - "$ref": "#/components/parameters/location_ids"
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  "/shipments/{id}":
    parameters:
    - name: id
      in: path
      description: Shipment ID
      required: true
      schema:
        "$ref": "#/components/schemas/ID"
    get:
      summary: Get Shipment
      tags:
      - Shipments
      description: Get shipment by ID for a location
      operationId: shipmentById
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Shipment"
    patch:
      summary: Update Shipment
      tags:
      - Shipments
      description: Update a shipment
      operationId: shipmentUpdate
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/ShipmentUpdateInput"
        required: true
        description: shipment parameters
  "/shipments/{id}/delivery_booking":
    parameters:
    - name: id
      in: path
      description: Shipment ID
      required: true
      schema:
        type: integer
    post:
      summary: Create Delivery Booking
      tags:
      - Shipments
      description: Create a delivery booking for a shipment
      operationId: shipmentDeliveryBooking
      responses:
        '200':
          description: successful
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: Token
  parameters:
    page:
      name: page
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        format: int32
        minimum: 1
    page_size:
      name: page_size
      in: query
      description: Number of items per page
      required: false
      schema:
        type: integer
        format: int32
        minimum: 1
    location_ids:
      name: location_ids
      in: query
      description: Comma separated list of location IDs
      required: false
      schema:
        type: array
        items:
          "$ref": "#/components/schemas/ID"
      style: form
      explode: false
  schemas:
    ID:
      type: integer
      format: int64
      minimum: 1
    NotFound:
      type: object
      properties:
        message:
          type: string
    Unauthorized:
      type: object
      properties:
        message:
          type: string
    PagedResponse:
      type: object
      required:
      - total_pages
      - total_count
      - current_page
      properties:
        total_pages:
          type: integer
          format: int32
        total_count:
          type: integer
          format: int32
        current_page:
          type: integer
          format: int32
      description: ''
    Deliveries:
      allOf:
      - "$ref": "#/components/schemas/PagedResponse"
      - type: object
        required:
        - payload
        properties:
          payload:
            type: array
            items:
              "$ref": "#/components/schemas/Delivery"
    Delivery:
      type: object
      required:
      - id
      - distributor_id
      - distributor_location_id
      properties:
        id:
          type: integer
          format: int64
        distributor_id:
          type: integer
          format: int64
        distributor_location_id:
          type: integer
          format: int64
    Shipments:
      allOf:
      - "$ref": "#/components/schemas/PagedResponse"
      - type: object
        required:
        - payload
        properties:
          payload:
            type: array
            items:
              "$ref": "#/components/schemas/Shipment"
    Shipment:
      type: object
      required:
      - id
      - distributor_id
      - distributor_location_id
      properties:
        id:
          "$ref": "#/components/schemas/ID"
        distributor_id:
          "$ref": "#/components/schemas/ID"
        distributor_location_id:
          "$ref": "#/components/schemas/ID"
    ShipmentUpdateInput:
      type: object
      properties:
        cartons:
          type: array
          items:
            type: object
            properties:
              id:
                "$ref": "#/components/schemas/ID"
              length_mm:
                type: integer
                format: int32
                minimum: 1
              width_mm:
                type: integer
                format: int32
                minimum: 1
              height_mm:
                type: integer
                format: int32
                minimum: 1
              weight_g:
                type: integer
                format: int32
                minimum: 1
              carton_items:
                type: array
                items:
                  type: object
                  properties:
                    shipment_unit_id:
                      "$ref": "#/components/schemas/ID"
                    quantity:
                      type: integer
                      format: int32
                      minimum: 1
    DistributorLocation:
      type: object
      required:
      - id
      - name
      properties:
        id:
          "$ref": "#/components/schemas/ID"
        name:
          type: string
        code:
          type: string
    Fulfillment:
      type: object
      required:
      - id
      - location_id
      - line_items
      properties:
        id:
          type: integer
          format: int64
        location_id:
          type: integer
          format: int64
        line_items:
          type: array
          items:
            "$ref": "#/components/schemas/FulfillmentLineItem"
    FulfillmentLineItem:
      type: object
      required:
      - id
      - fulfillment_id
      - sku
      - quantity
      properties:
        id:
          type: integer
          format: int64
        fulfillment_id:
          type: integer
          format: int64
        sku:
          type: string
        quantity:
          type: integer
          format: int32
  responses:
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            "$ref": "#/components/schemas/Unauthorized"
security:
- bearerAuth: []
servers:
- url: http://api.partbot.local:3000/v1/distributor
  description: Development server
