Rails.application.routes.draw do
  # Sidekiq UI. Redirect to Dashboard if User is not admin
  require "sidekiq_unique_jobs/web"
  require "sidekiq-status/web"
  require "sidekiq/throttled/web"

  mount ActionCable.server => "/cable"

  admin_constraint =
    lambda do |request|
      user = User.find_by(id: request.session[:user_id])
      user.present? && user.has_role?(:admin)
    end
  mount Sidekiq::Web, at: "/sidekiq", constraints: admin_constraint
  match "/sidekiq" => "dashboard#index", :via => [:get]

  mount Coverband::Reporters::Web.new, at: "/coverage", constraints: admin_constraint
  match "/coverage" => "dashboard#index", :via => [:get]

  constraints subdomain: "tracking" do
    get "/", to: "tracking#not_found"
    get "/:tracking_number", to: "tracking#show", as: :tracking
  end

  namespace :distributor, path: "" do
    constraints subdomain: "distributor" do
      get "/", to: "distributor#index"

      scope path: "l/:distributor_location_id", as: "location" do
        resources :fulfillments, only: %i[index show] do
          resource :ship, only: %i[create], path: "ship"
          resources :deliveries, only: %i[create]
        end

        resources :shipments, only: %i[index] do
          resource :bring_your_own_shipping, only: %i[create], path: "byo"
        end

        resources :handovers, only: %i[index create] do
        end
      end

      resources :deliveries, only: %i[update destroy] do
        resource :shipment_booking, only: %i[create destroy], path: "booking"
        resource :shipment_label, only: %i[create destroy], path: "label"
        resource :shipment_pickup, only: %i[show create destroy], path: "pickup"
        resource :shipment_dropoff, only: %i[create], path: "dropoff"
        resource :shipment_manual,
                 only: %i[show create],
                 path: "manual",
                 controller: "shipment_manuals"
        resources :delivery_parcels, only: %i[create], path: "parcels"
      end

      resources :label_requests,
                only: %i[index show create],
                path: "label_requests",
                controller: "shipment_manuals"

      resources :delivery_parcels, only: %i[update destroy] do
        resources :parcel_items, only: %i[create], path: "items"
      end

      resources :parcel_items, only: %i[update destroy]

      # get "/fulfillments", to: "distributor#fulfillments"
      # get "/fulfillments/:id", to: "distributor#fulfillment"

      # post "/fulfillments/:id/deliveries", to: "distributor#create_fulfillment_delivery"
      # patch "/fulfillments/:id/deliveries/:delivery_id",
      #       to: "distributor#update_fulfillment_delivery"
      # delete "/fulfillments/:id/deliveries/:delivery_id",
      #        to: "distributor#delete_fulfillment_delivery"

      # post "/fulfillments/:id/deliveries/:delivery_id/parcels",
      #      to: "distributor#create_fulfillment_parcel"
      # patch "/fulfillments/:id/deliveries/:delivery_id/parcels/:parcel_id",
      #       to: "distributor#update_fulfillment_parcel"
      # delete "/fulfillments/:id/deliveries/:delivery_id/parcels/:parcel_id",
      #        to: "distributor#delete_fulfillment_parcel"
      # get "/fulfillments/:id/deliveries/:delivery_id/pickup-slots",
      #     to: "distributor#fulfillment_delivery_pickup_slots"
      # post "/fulfillments/:id/deliveries/:delivery_id/book",
      #      to: "distributor#fulfillment_delivery_book"

      # delete "/fulfillments/:id/deliveries/:delivery_id/parcels/:parcel_id/items/:item_id",
      #        to: "distributor#delete_fulfillment_parcel_item"

      # post "/fulfillments/:id/units/:unit_id/update_from_package/:package_id",
      #      to: "distributor#update_fulfillment_unit_from_package"
      # post "/fulfillments/:id/units/:unit_id/allocate", to: "distributor#allocate_fulfillment_unit"
      # post "/fulfillments/:id/units/:unit_id/move", to: "distributor#move_fulfillment_unit"

      get "/fulfillments-search", to: "distributor#fulfillment_search"
      get "/locations", to: "distributor#locations"
      get "/capture", to: "distributor#capture"

      post "/box", to: "distributor#create_box"
      delete "/box/:id", to: "distributor#destroy_box"

      get "/account", to: "distributor#account"

      get "/inventory", to: "distributor#inventory"

      get "/inventory/datatable", to: "distributor#inventory_datatable", defaults: { format: :json }
      post "/inventory/datatable",
           to: "distributor#inventory_datatable",
           defaults: {
             format: :json
           }

      get "/pricing", to: "distributor#pricing"
      get "/pricing/datatable", to: "distributor#pricing_datatable", defaults: { format: :json }
      post "/pricing/datatable", to: "distributor#pricing_datatable", defaults: { format: :json }

      get "/payments", to: "distributor#payments"
      get "/payments/link", to: "distributor#payments_link"
      get "/payments/connected", to: "distributor#payments_connected"

      resources :distributors do
        member { post "/locations", to: "distributor#create_location" }
        resources :distributor_locations, path: "locations", as: :locations do
          post "/", to: "distributor#create_location", as: :create_location
          patch "/", to: "distributor#update_location", as: :update_location
        end
      end

      get "/*path", to: "distributor#index"
    end
  end

  namespace :marketplace, path: "" do
    constraints subdomain: "marketplace" do
      get "/welcome", to: "marketplace#welcome"
      post "/onboard/:user_id", to: "marketplace#onboard"
      post "/shopify-onboard-complete/:user_id", to: "marketplace#shopify_onboarding_complete"

      get "/", to: "marketplace#index"
      get "/import", to: "marketplace#import"
      get "/billing", to: "marketplace#billing"
      get "/account", to: "marketplace#account"
      get "/products", to: "marketplace#products"
      get "/distributors", to: "marketplace#distributors"
      get "/settings", to: "marketplace#settings"
      get "/eula", to: "marketplace#eula"
      patch "/eula", to: "marketplace#eula_accepted"
      get "/team", to: "marketplace#team"

      # new Shopify App Store install auth
      get "/shopify-app-install", to: "shopify_integrations#app_store_install_auth"

      resources :fulfillment_orders, path: :fulfillments

      resources :stores do
        collection do
          get "install", to: "stores#connect_new_store", as: :connect_new

          get "install/woocommerce",
              to: "woocommerce_integrations#install",
              as: :woocommerce_install
          post "install/woocommerce",
               to: "woocommerce_integrations#connect_shop",
               as: :connect_woocommerce_public
          delete ":integration_id/woocommerce",
                 to: "woocommerce_integrations#disconnect_shop",
                 as: :disconnect_woocommerce_public

          get "install/maropost", to: "maropost_integrations#install", as: :maropost_install
          post "install/maropost",
               to: "maropost_integrations#connect_shop",
               as: :connect_maropost_public
          delete ":integration_id/maropost",
                 to: "maropost_integrations#disconnect_shop",
                 as: :disconnect_maropost_public

          get ":id/maropost/settings",
              to: "maropost_integrations#settings",
              as: :maropost_integration_settings

          patch ":id/maropost/settings",
                to: "maropost_integrations#update_settings",
                as: :edit_maropost_integration_settings

          get ":id/maropost/fetch-warehouses", to: "maropost_integrations#fetch_warehouses"

          get "install/shopify", to: "shopify_integrations#install", as: :shopify_install
          post "install/shopify",
               to: "shopify_integrations#connect_shop",
               as: :connect_shopify_public
          delete ":integration_id/shopify",
                 to: "shopify_integrations#disconnect_shop",
                 as: :disconnect_shopify_public
          get ":id/shopify/reauth", to: "shopify_integrations#reauth_shop", as: :reconnect_shopify

          #get '/shopify/dashboard', to: 'shopify_integrations#dashboard', as: :shopify_dashboard
          get "/shopify/account", to: "shopify_integrations#account", as: :shopify_account

          get ":id/shopify/modules", to: "shopify_integrations#modules", as: :shopify_modules
          get ":id/shopify/fulfillments/",
              to: "shopify_integrations#fulfillments",
              as: :shopify_fulfillments
          get ":id/shopify/fulfillments/datatable",
              to: "shopify_integrations#fulfillments_datatable",
              as: :shopify_fulfillments_datatable

          # http://marketplace.partbot.io/stores/25/shopify/create-fulfilment-payment-intent

          post ":id/shopify/create-fulfilment-payment-intent",
               to: "marketplace#create_fulfilment_payment_intent"

          post ":id/shopify/update-store-auto-billing", to: "marketplace#update_store_auto_billing"

          get ":id/shopify/modules/fulfilment",
              to: "shopify_integrations#fulfilment_module_settings",
              as: :fulfilment_module_settings
          patch ":id/shopify/modules/fulfilment",
                to: "shopify_integrations#edit_fulfilment_module_settings",
                as: :edit_fulfilment_module_settings

          # Carrier Service Module
          get ":id/shopify/modules/shipping-rates",
              to: "shopify_integrations#carrier_module_settings",
              as: :carrier_module_settings
          patch ":id/shopify/modules/shipping-rates",
                to: "shopify_integrations#edit_carrier_module_settings",
                as: :edit_carrier_module_settings
        end
      end

      post "/auth/woocommerce/public",
           to: "woocommerce_integrations#install_callback",
           as: :woocommerce_public_callback
      get "/auth/shopify/public",
          to: "shopify_integrations#install_callback",
          as: :shopify_public_callback

      # Woocommerce Install
      # get '/woocommerce/install', to: 'woocommerce_integrations#install', as: :woocommerce_install
      # post '/woocommerce/install', to: 'woocommerce_integrations#connect_shop', as: :connect_woocommerce_public
      # post '/auth/woocommerce/public', to: 'woocommerce_integrations#install_callback', as: :woocommerce_public_callback
    end
  end

  namespace :shipping do
    resources :dangerous_goods_definitions, only: %i[index]
  end

  post "/marketplace/webhook", to: "marketplace/stripe_webhooks#webhook"

  root "partbot#index"
  get "/privacy", to: "partbot#privacy"
  get "/terms", to: "partbot#terms"
  get "/cookies", to: "partbot#cookies"
  get "/acceptable-use", to: "partbot#acceptable_use"

  get "/team", to: "account#team"

  post "/product-type", to: "admin/product_types#create"

  resources :vehicles do
    collection do
      get :datatable
      get :makes, as: :vehicle_makes
      get :models, as: :vehicle_models
      get :bodies, as: :vehicle_bodies
      get :fuel_types, as: :vehicle_fuel_types
      get :drive_types, as: :vehicle_drive_types
      get :transmissions, as: :vehicle_transmissions
    end
    member { get :changes }
  end

  resources :products do
    collection do
      get :datatable
      post :datatable
    end
  end

  namespace :brand, path: "/b" do
    scope ":brand_id" do
      get "/products/issues", to: "products#issues", as: :product_issues
      get "/products/issues/datatable",
          to: "products#datatable_issues",
          as: :product_issues_datatable,
          defaults: {
            format: :json
          }

      get "/products/activity/datatable",
          to: "products#datatable_activity",
          as: :product_activity_datatable,
          defaults: {
            format: :json
          }
      post "/products/import", to: "products#import", defaults: { format: :json }

      post "/products/reindex", to: "products#reindex_products", defaults: { format: :json }

      # Bulk Actions
      post "/products/bulk-publish", to: "products#bulk_publish", defaults: { format: :json }
      post "/products/bulk-unpublish", to: "products#bulk_unpublish", defaults: { format: :json }
      post "/products/bulk-create-listings",
           to: "products#bulk_create_listings",
           defaults: {
             format: :json
           }

      post "/products/bulk-publish-listings",
           to: "products#bulk_publish_listings",
           defaults: {
             format: :json
           }

      resources :products do
        resources :product_media, path: :media, only: %i[index create show update destroy] do
          put "make_primary", on: :member, to: "product_media#make_primary"
        end

        resources :product_charts, path: :charts, only: %i[index create show update destroy]
        collection do
          get :datatable
          post :datatable
        end

        # collection { get :datatable_issues }
        member do
          get :vehicle_applications
          get :cross_sell_products
          get :product_attributes
          get :packages
          get :hazards
          get :parts
          delete :remove_vehicles
          post :parts, to: "products#add_parts", defaults: { format: :json }
          put :parts, to: "products#update_parts", defaults: { format: :json }
          post "copy-from/:source_id", to: "products#copy_from", defaults: { format: :json }
          post "copy-to", to: "products#copy_to", defaults: { format: :json }
          delete :parts, to: "products#remove_parts", defaults: { format: :json }
          post "create-marketplace-listing",
               to: "products#create_marketplace_listing",
               defaults: {
                 format: :json
               }

          get :audits
          post "audits/:audit_id/revert", to: "products#revert_audit", defaults: { format: :json }
        end
      end

      resources :listings do
        collection do
          get :datatable
          get :search
          get :shops, to: "listings#integrations"
        end

        member { patch :status, to: "listings#change_status" }
        member { patch :visibility, to: "listings#set_visibility" }
      end

      resources :amazon_listings do
        collection { post :export, to: "amazon_listings#export", defaults: { format: :json } }
      end

      resources :settings
      resources :templates
    end
  end

  namespace :team, path: "/team" do
    resources :brands
  end

  namespace :admin, path: "/admin" do
    get "/monitoring/http-requests", to: "http_request_monitor#stats", constraints: admin_constraint
    get "/billing/revenue-share-charges",
        to: "billing#revenue_share_charges",
        constraints: admin_constraint
    post "/billing/revenue-share-charges/:charge_id/create-invoice",
         to: "billing#create_revenue_share_invoice",
         constraints: admin_constraint,
         as: :create_revenue_share_invoice

    resources :products
    resources :listings
    resources :brands do
      member do
        # /admin/brands/179/product-attributes-template

        # create brand product attributes template
        post "/product-attributes-template",
             to: "brands#create_product_attributes_template",
             as: :create_product_attributes_template

        # update brand product attributes template
        patch "/product-attributes-template/:product_attributes_template_id",
              to: "brands#update_product_attributes_template",
              as: :update_product_attributes_template
      end
    end

    resources :attribute_titles, path: "attributes" do
      member do
        get :export_with_products,
            to: "attribute_titles#export_with_products",
            as: :export_attribute_with_products
        post :add_value
      end
    end
    resources :sub_brands

    resources :distributors do
      member { post "/locations", to: "distributors#create_location" }
      resources :distributor_locations, path: "locations" do
        member do
          put "/locations/:id", to: "distributors#update_location"
          get :shipping_and_handling, to: "distributors#shipping_and_handling"
          put :shipping_and_handling, to: "distributors#update_shipping_and_handling"
        end
      end
    end

    resources :vehicles do
      collection do
        get :datatable
        get :makes, as: :vehicle_makes
        get :models, as: :vehicle_models
        get :bodies, as: :vehicle_bodies
        get :fuel_types, as: :vehicle_fuel_types
        get :drive_types, as: :vehicle_drive_types
        get :transmissions, as: :vehicle_transmissions
      end
      member do
        get :links, as: :vehicle_links
        post :reindex
        post :external_link
        post :external_unlink
        post :external_override
      end
    end

    # get '/vehicles/makes', to: 'vehicles#makes', as: 'vehicle_makes'
    # get '/vehicles/models', to: 'vehicles#models', as: 'vehicle_models'
    # get '/vehicles/bodies', to: 'vehicles#bodies', as: 'vehicle_bodies'
    # get '/vehicles/fuel_types', to: 'vehicles#fuel_types', as: 'vehicle_fuel_types'
    # get '/vehicles/drive_types', to: 'vehicles#drive_types', as: 'vehicle_drive_types'
    # get '/vehicles/transmissions', to: 'vehicles#transmissions', as: 'vehicle_transmissions'

    # get '/vehicles/:id/links', to: 'vehicles#links', as: 'vehicle_links'
    # post '/vehicles/:id/reindex', to: 'vehicles#reindex'
    # post '/vehicles/:id/external_link', to: 'vehicles#external_link'
    # post '/vehicles/:id/external_unlink', to: 'vehicles#external_unlink'
    # post '/vehicles/:id/external_override', to: 'vehicles#external_override'

    resources :external_vehicles do
      collection do
        get :list
        get :global_translation
      end
      member do
        get :translations, to: "external_vehicles#translations"
        put :translations, to: "external_vehicles#update_translations"
      end
    end

    # get '/external_vehicles/list', to: 'external_vehicles#list'
    # get '/external_vehicles/global_translation', to: 'external_vehicles#global_translation'
    # get '/external_vehicles/:id/translations', to: 'external_vehicles#translations'
    # put '/external_vehicles/:id/translations', to: 'external_vehicles#update_translations'

    # resources :categories
    # resources :brands

    resources :products do
      collection { get :datatable }
      member do
        get :vehicle_applications
        get :cross_sell_products
        get :product_attributes
        get :packages
        get :parts
        delete :remove_vehicles
        post :parts, to: "products#add_parts", defaults: { format: :json }
        delete :parts, to: "products#remove_parts", defaults: { format: :json }
      end
    end

    resources :collections
    get "collections/:parent_id/child", to: "collections#new_child", as: :new_child
    get "back", to: "collections#go_back", as: :go_back
    post "collections/update-collection-order", to: "collections#update_order"
    resources :product_types, path: "/product-types"
    resources :listings do
      collection { get :datatable }
    end

    # Exports
    resources :exports
    get "/export/:product_type_id/:debtor_code", to: "exports#export_csv"
  end

  # Demo Signup
  post "/demo", to: "partbot#demo"

  match "/delayed_job" => DelayedJobWeb, :anchor => false, :via => %i[get post]

  get "/dashboard", to: "dashboard#index"
  get "/settings", to: "dashboard#settings"
  get "/lab", to: "dashboard#lab"

  get "/account", to: "account#index"

  get "/integrations", to: "integrations#index"
  post "/integrations/shopify", to: "integrations#connect_shop", as: :connect_shopify
  get "/integrations/shopify/reauth/:id", to: "integrations#reauth_shop", as: :reconnect_shopify
  delete "/integrations/shopify",
         to: "integrations#delete_shopify_integration",
         as: :delete_shopify_integration
  get "/integrations/shopify/sync/:id",
      to: "integrations#sync_shopify_integration",
      as: :sync_shopify_integration
  resources :shopify_integrations

  get "/auth/shopify/callback", to: "integrations#shopify_callback", as: :shopify_callback

  # Shopify Fulfilment Service
  get "/fulfilment/:integration_id",
      to: "shopify_fulfillment_service#create",
      as: :create_shopify_fulfillment_service

  delete "/fulfilment/:integration_id",
         to: "shopify_fulfillment_service#delete",
         as: :delete_shopify_fulfillment_service

  get "/fulfillment/fetch_stock", to: "shopify_fulfillment_service#fetch_stock"
  post "/fulfillment/create_hook", to: "shopify_fulfillment_service#create_hook"
  post "/fulfillment/update_hook", to: "shopify_fulfillment_service#update_hook"

  # TODO(liam)
  # post '/fulfillment/shipping_and_handling',to: 'fulfillment_service#create_'

  resources :users do
    member do
      post :brand
      post "favourite-brand", to: "users#favourite_brand"
      delete "favourite-brand", to: "users#unfavourite_brand"
    end
  end

  get "/search", to: "listings#search"
  get "/shops", to: "listings#integrations"

  get "/product_types_search", to: "admin/product_types#search"
  get "/product_categories_search", to: "admin/product_types#search_categories"
  get "/vehicle_field_search", to: "admin/vehicles#field_search"
  get "/country_search", to: "account#country_search"
  get "/timezone_search", to: "account#timezone_search"
  get "/currency_search", to: "account#currency_search"
  get "/brand_search", to: "admin/brands#search"
  get "/sub_brand_search", to: "admin/sub_brands#search"
  get "/attribute_search", to: "admin/attribute_titles#search"
  get "/attribute/:attribute_title_id/values", to: "admin/attribute_titles#get_attribute_values"

  resources :product_attributes
  post "/reorder_product_attributes", to: "product_attributes#reorder_product_attributes"

  resources :vehicle_attributes
  resources :attribute_values
  resources :vehicle_applications
  resources :vehicle_application_attributes

  resources :packages
  resources :hazards
  resources :cross_sell_products
  resources :product_images
  resources :export_templates
  resources :export_template_fields

  get "/images", to: "images#index", as: :images
  post "/images", to: "images#generate_zip", as: :images_generate_zip

  constraints(subdomain: /files(?:\.(local|dev|test|staging))?/) do
    get "exports/customers/:id", to: "files#download"
  end

  namespace :api, path: "" do
    constraints subdomain: "api" do
      mount Rswag::Ui::Engine => "/docs"
      mount Rswag::Api::Engine => "/docs"
      namespace :v1, defaults: { format: :json } do
        resources :parts do
          member do
            get "/identifiers", to: "parts#identifiers", as: :identifiers
            get "/vehicles", to: "parts#vehicles", as: :vehicles
            patch "/property", to: "parts#update_property", as: :update_property
            delete "/vehicles", to: "parts#clear_vehicles", as: :clear_vehicles
          end
        end

        resources :assemblies do
          member do
            get "/identifiers", to: "assemblies#identifiers", as: :identifiers
            get "/vehicles", to: "assemblies#vehicles", as: :vehicles
            patch "/property", to: "assemblies#update_property", as: :update_property
          end
        end

        get "/monitor/heartbeat", to: "monitor#heartbeat"
        post "/monitor/heartbeat", to: "monitor#heartbeat"

        get "/product/:id", to: "customer#get_product_by_slug"
        get "/products/:id", to: "customer#show_product"

        get "/product/:id/product-numbers", to: "products#get_product_numbers"
        post "/product/:id/product-number", to: "products#update_product_number"
        delete "/product/:id/product-number", to: "products#delete_product_number"

        get "/pricing", to: "customer#get_account_price"
        post "/order", to: "customer#submit_order"

        get "/inventory/:id", to: "customer#get_inventory"

        resources :collections

        get "/vehicles/makes", to: "vehicles#get_makes"
        resources :vehicles
        post "/vehicles/find", to: "vehicles#find", as: :find_vehicles
        post "/vehicles/auth", to: "vehicles#auth"
        post "/vehicles/plate-search", to: "vehicles#plate_search"
        post "/vehicles/plate-search-retry", to: "vehicles#plate_search_detailed"

        resources :product_types
        resources :brands

        post "/fulfillments/quote", to: "fulfillments#quote"

        get "/clearance-menu",
            to: "collections#get_clearance_collections",
            as: :get_clearance_collections

        # Catalogue API
        namespace :catalogue do
        end

        # Distributor API
        namespace :distributor do
          resources :locations, only: %i[index show]
          resources :fulfillments, only: %i[index show] do
            collection do
              resource :statistics,
                       only: %i[show],
                       module: :fulfillments,
                       controller: "statistics",
                       as: :fulfillments_statistics
            end
            resource :fulfillment_request,
                     only: %i[update],
                     module: :fulfillments,
                     controller: "fulfillment_request"
            resource :cancellation_request,
                     only: %i[update],
                     module: :fulfillments,
                     controller: "cancellation_request"
            resource :change_location,
                     only: %i[update],
                     module: :fulfillments,
                     controller: "change_location"
          end
          resources :shipments, only: %i[index show update] do
            collection do
              resource :statistics,
                       only: %i[show],
                       module: :shipments,
                       controller: "statistics",
                       as: :shipments_statistics
            end
            resource :delivery_booking,
                     only: %i[create],
                     module: :shipments,
                     controller: "delivery_booking"
            resource :quote, only: %i[create], module: :shipments
            resource :cancel, only: %i[create], module: :shipments
            resources :units, only: %i[create destroy], module: :shipments, controller: "units"
          end
          resources :deliveries, only: %i[index show destroy] do
            collection do
              resource :pickup, only: %i[create], module: :deliveries, controller: "pickup"
            end
            resource :documents, only: %i[show], module: :deliveries
          end
        end

        # Marketplace API
        namespace :marketplace do
          # TODO: Refactor this to use resources
          get "/listing/:id", to: "marketplace#listing"

          # TODO: Refactor this to use resources
          get "/import-list", to: "marketplace#get_import_list"
          put "/import-list/:id", to: "marketplace#update_import_listing"
          delete "/import-list", to: "marketplace#bulk_destroy_import_listings"
          delete "/import-list/:id", to: "marketplace#destroy_import_listing"
          post "/import-list/:id/publish", to: "marketplace#publish_import_listing"
          post "/import-list/publish", to: "marketplace#bulk_publish_import_listing"
          get "/import-list/progress", to: "marketplace#bulk_publish_progress"

          # TODO: Refactor this to use resources
          get "/stores", to: "marketplace#get_stores"

          post "/all-search-results", to: "marketplace#all_search_results"
          post "/bulk-create-import-listing", to: "marketplace#bulk_create_import_listing"
          post "/create-import-listing", to: "marketplace#create_import_listing"
          post "/all-facet-filters", to: "marketplace#all_facet_filters"

          # TODO: Refactor this to use resources
          post "/add-team-domain", to: "marketplace#add_team_domain"
          post "/remove-team-domain", to: "marketplace#remove_team_domain"

          post "/add-tecalliance-merchant", to: "marketplace#add_tecalliance_merchant"
          post "/remove-tecalliance-merchant", to: "marketplace#remove_tecalliance_merchant"

          post "/update-team", to: "marketplace#update_team"
          post "/update-account", to: "marketplace#update_account"
          post "/update-setting", to: "marketplace#update_setting"
          post "/update-store-auto-billing", to: "marketplace#update_store_auto_billing"

          resources :merchant_products, only: %i[index show update] do
            resources :merchant_product_attributes, only: %i[create destroy]
            resources :merchant_product_vehicles, only: %i[create destroy]

            member { post "/send_to_pim", to: "merchant_products#send_to_pim" }

            collection do
              post "/sync_all", to: "merchant_products#sync_all"
              delete "/cancel_sync_all", to: "merchant_products#cancel_sync_all"
              get "/sync_progress", to: "merchant_products#sync_progress"
            end
          end

          post "/connect-distributor-account", to: "marketplace#connect_distributor_account"
          delete "/disconnect-distributor-account/:id",
                 to: "marketplace#disconnect_distributor_account"
        end

        # Billing and subscription endpoints
        post "/billing/add-plate-search", to: "billing#add_plate_search"
        post "/billing/get-plan-url", to: "billing#get_plan_url"
        post "/billing/cancel-shopify-plan", to: "billing#cancel_shopify_plan"

        # DEPRECATED ROUTES
        # resources :fulfillment_orders, path: :fulfillments, only: %i[index show update] do
        #   member do
        #     post "request-fulfillment", to: "fulfillment_orders#request_fulfillment"
        #     post "cancel-fulfillment", to: "fulfillment_orders#cancel_fulfillment"
        #     post "resync"
        #   end
        # end

        # Shipping Calculator
        post "/shipping/get-rates", to: "shipping#get_shipping_calculator_plugin_rates"
      end
    end
  end

  get "/auth/auth0/callback" => "auth0#callback"
  get "/auth/failure" => "auth0#failure"

  get "/logout" => "logout#logout"

  post "/webhooks/tableflow-import" => "webhooks#tableflow_import"

  post "/webhooks/woocommerce/order/create" => "webhooks#woocommerce_order_create"
  post "/webhooks/woocommerce/order/update" => "webhooks#woocommerce_order_update"

  post "/webhooks/easyship" => "webhooks#easyship"

  post "/webhooks/shopify" => "webhooks#shopify"
  post "/webhooks/merchant_product/shopify_product_create" =>
         "webhooks#create_shopify_merchant_product"
  post "/webhooks/merchant_product/shopify_product_update" =>
         "webhooks#update_shopify_merchant_product"
  post "/webhooks/merchant_product/shopify_product_delete" =>
         "webhooks#delete_shopify_merchant_product"
  post "/webhooks/merchant_product/woocommerce_product_create" =>
         "webhooks#create_woocommerce_merchant_product"
  post "/webhooks/merchant_product/woocommerce_product_update" =>
         "webhooks#update_woocommerce_merchant_product"
  post "/webhooks/merchant_product/woocommerce_product_delete" =>
         "webhooks#delete_woocommerce_merchant_product"

  # Mandatory Shopify App Webhooks for GDPR compliancy
  post "/webhooks/customer-data-request" => "webhooks#shopify_customer_data_request"
  post "/webhooks/customer-data-erasure" => "webhooks#shopify_customer_redact"
  post "/webhooks/shop-data-erasure" => "webhooks#shopify_store_redact"

  # Shopify App Webhooks
  post "/webhooks/billing/failure" => "webhooks#shopify_app_billing_failure"
  post "/webhooks/billing/success" => "webhooks#shopify_app_billing_success"
  post "/webhooks/shopify/app/uninstalled" => "webhooks#shopify_app_uninstalled"

  get "/shopify/carrier/create/:integration_id" => "shipping#create_shopify_carrier_service",
      :as => :create_shopify_carrier_service

  delete "/shopify/carrier/:integration_id" => "shipping#delete_shopify_carrier_service",
         :as => :delete_shopify_carrier_service

  post "/shopify/carrier/get-shipping-rates" => "shipping#get_shopify_rates",
       :as => :shopify_carrier_service_callback

  # Easyship API Routes
  post "/shipping/get-rates" => "shipping#easyship_rates", :as => :shipping_get_rates
  post "/shipping/create-shipment" => "shipping#create_easyship_shipment",
       :as => :shipping_create_shipment
  delete "/shipping/delete-shipment/:easyship_shipment_id" => "shipping#delete_easyship_shipment",
         :as => :shipping_delete_shipment
  patch "/shipping/update-shipment/:easyship_shipment_id" => "shipping#update_easyship_shipment",
        :as => :shipping_update_shipment
  post "/shipping/buy-labels" => "shipping#buy_labels_easyship", :as => :shipping_buy_labels
  get "/shipping/pickup-slots/:courier_id" => "shipping#pickup_slots", :as => :shipping_pickup_slots
  post "/shipping/request-pickup" => "shipping#request_pickup", :as => :shipping_request_pickup
  post "/shipping/direct-handover" => "shipping#direct_handover", :as => :shipping_direct_handover

  get "/fulfil", to: "integrations#fulfil_order", as: :fulfilment
  post "/confirm-booking", to: "integrations#confirm_booking", as: :confirm_booking
  get "/booking/success", to: "integrations#booking_success", as: :booking_success
  get "/track", to: "integrations#track", as: :track

  constraints subdomain: "images" do
    get "/:id" => "shortener/shortened_urls#show"
  end

  get "/vehicle-search-ui" => "vehicle_search#index"

  post "/upload-images", to: "images#upload_multiple_images", as: :images_upload
  post "/export_template/export/:id", to: "export_templates#export", as: :export_template_export
  post "/invite-team-member", to: "users#invite_team_member", as: :invite_team_member
  delete "/remove-team-member/:id", to: "users#remove_team_member", as: :remove_team_member
  post "/product-issues", to: "product_issues#create", as: :product_issues_create
  patch "/product-issues/:id", to: "product_issues#update_status", as: :product_issues_update_status
  get "/product-issues/:id", to: "product_issues#product_issues", as: :product_issues

  constraints subdomain: "" do
    namespace :integrations do
      scope :shopify, as: :shopify do
        post "webhooks", to: "shopify#webhooks"
        scope :callbacks do
          get "fetch_stock", to: "shopify#fetch_stock"
          get "fetch_tracking_numbers", to: "shopify#fetch_tracking_numbers"
          post "fulfillment_order_notification", to: "shopify#fulfillment_order_notification"
        end
      end

      scope :woocommerce, as: :woocommerce do
        post "webhooks", to: "woocommerce#webhooks"
      end

      scope :stripe, as: :stripe do
        post "webhooks", to: "stripe#webhooks"
      end

      scope :maropost, as: :maropost do
        post "webhooks", to: "maropost#webhooks"
      end
    end
  end
end
